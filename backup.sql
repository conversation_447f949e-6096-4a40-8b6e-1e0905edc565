--
-- PostgreSQL database dump
--

-- Dumped from database version 15.8
-- Dumped by pg_dump version 15.8

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: _realtime; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA _realtime;


ALTER SCHEMA _realtime OWNER TO supabase_admin;

--
-- Name: auth; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA auth;


ALTER SCHEMA auth OWNER TO supabase_admin;

--
-- Name: pg_cron; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_cron WITH SCHEMA pg_catalog;


--
-- Name: EXTENSION pg_cron; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pg_cron IS 'Job scheduler for PostgreSQL';


--
-- Name: extensions; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA extensions;


ALTER SCHEMA extensions OWNER TO postgres;

--
-- Name: graphql; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA graphql;


ALTER SCHEMA graphql OWNER TO supabase_admin;

--
-- Name: graphql_public; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA graphql_public;


ALTER SCHEMA graphql_public OWNER TO supabase_admin;

--
-- Name: pg_net; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_net WITH SCHEMA extensions;


--
-- Name: EXTENSION pg_net; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pg_net IS 'Async HTTP';


--
-- Name: pgbouncer; Type: SCHEMA; Schema: -; Owner: pgbouncer
--

CREATE SCHEMA pgbouncer;


ALTER SCHEMA pgbouncer OWNER TO pgbouncer;

--
-- Name: realtime; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA realtime;


ALTER SCHEMA realtime OWNER TO supabase_admin;

--
-- Name: storage; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA storage;


ALTER SCHEMA storage OWNER TO supabase_admin;

--
-- Name: supabase_functions; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA supabase_functions;


ALTER SCHEMA supabase_functions OWNER TO supabase_admin;

--
-- Name: vault; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA vault;


ALTER SCHEMA vault OWNER TO supabase_admin;

--
-- Name: pg_graphql; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_graphql WITH SCHEMA graphql;


--
-- Name: EXTENSION pg_graphql; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pg_graphql IS 'pg_graphql: GraphQL support';


--
-- Name: pg_stat_statements; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_stat_statements WITH SCHEMA extensions;


--
-- Name: EXTENSION pg_stat_statements; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pg_stat_statements IS 'track planning and execution statistics of all SQL statements executed';


--
-- Name: pgcrypto; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pgcrypto WITH SCHEMA extensions;


--
-- Name: EXTENSION pgcrypto; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pgcrypto IS 'cryptographic functions';


--
-- Name: pgjwt; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pgjwt WITH SCHEMA extensions;


--
-- Name: EXTENSION pgjwt; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pgjwt IS 'JSON Web Token API for Postgresql';


--
-- Name: supabase_vault; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS supabase_vault WITH SCHEMA vault;


--
-- Name: EXTENSION supabase_vault; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION supabase_vault IS 'Supabase Vault Extension';


--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA extensions;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: aal_level; Type: TYPE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TYPE auth.aal_level AS ENUM (
    'aal1',
    'aal2',
    'aal3'
);


ALTER TYPE auth.aal_level OWNER TO supabase_auth_admin;

--
-- Name: code_challenge_method; Type: TYPE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TYPE auth.code_challenge_method AS ENUM (
    's256',
    'plain'
);


ALTER TYPE auth.code_challenge_method OWNER TO supabase_auth_admin;

--
-- Name: factor_status; Type: TYPE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TYPE auth.factor_status AS ENUM (
    'unverified',
    'verified'
);


ALTER TYPE auth.factor_status OWNER TO supabase_auth_admin;

--
-- Name: factor_type; Type: TYPE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TYPE auth.factor_type AS ENUM (
    'totp',
    'webauthn',
    'phone'
);


ALTER TYPE auth.factor_type OWNER TO supabase_auth_admin;

--
-- Name: one_time_token_type; Type: TYPE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TYPE auth.one_time_token_type AS ENUM (
    'confirmation_token',
    'reauthentication_token',
    'recovery_token',
    'email_change_token_new',
    'email_change_token_current',
    'phone_change_token'
);


ALTER TYPE auth.one_time_token_type OWNER TO supabase_auth_admin;

--
-- Name: action; Type: TYPE; Schema: realtime; Owner: supabase_admin
--

CREATE TYPE realtime.action AS ENUM (
    'INSERT',
    'UPDATE',
    'DELETE',
    'TRUNCATE',
    'ERROR'
);


ALTER TYPE realtime.action OWNER TO supabase_admin;

--
-- Name: equality_op; Type: TYPE; Schema: realtime; Owner: supabase_admin
--

CREATE TYPE realtime.equality_op AS ENUM (
    'eq',
    'neq',
    'lt',
    'lte',
    'gt',
    'gte',
    'in'
);


ALTER TYPE realtime.equality_op OWNER TO supabase_admin;

--
-- Name: user_defined_filter; Type: TYPE; Schema: realtime; Owner: supabase_admin
--

CREATE TYPE realtime.user_defined_filter AS (
	column_name text,
	op realtime.equality_op,
	value text
);


ALTER TYPE realtime.user_defined_filter OWNER TO supabase_admin;

--
-- Name: wal_column; Type: TYPE; Schema: realtime; Owner: supabase_admin
--

CREATE TYPE realtime.wal_column AS (
	name text,
	type_name text,
	type_oid oid,
	value jsonb,
	is_pkey boolean,
	is_selectable boolean
);


ALTER TYPE realtime.wal_column OWNER TO supabase_admin;

--
-- Name: wal_rls; Type: TYPE; Schema: realtime; Owner: supabase_admin
--

CREATE TYPE realtime.wal_rls AS (
	wal jsonb,
	is_rls_enabled boolean,
	subscription_ids uuid[],
	errors text[]
);


ALTER TYPE realtime.wal_rls OWNER TO supabase_admin;

--
-- Name: buckettype; Type: TYPE; Schema: storage; Owner: supabase_storage_admin
--

CREATE TYPE storage.buckettype AS ENUM (
    'STANDARD',
    'ANALYTICS'
);


ALTER TYPE storage.buckettype OWNER TO supabase_storage_admin;

--
-- Name: email(); Type: FUNCTION; Schema: auth; Owner: supabase_auth_admin
--

CREATE FUNCTION auth.email() RETURNS text
    LANGUAGE sql STABLE
    AS $$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.email', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'email')
  )::text
$$;


ALTER FUNCTION auth.email() OWNER TO supabase_auth_admin;

--
-- Name: FUNCTION email(); Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON FUNCTION auth.email() IS 'Deprecated. Use auth.jwt() -> ''email'' instead.';


--
-- Name: jwt(); Type: FUNCTION; Schema: auth; Owner: supabase_auth_admin
--

CREATE FUNCTION auth.jwt() RETURNS jsonb
    LANGUAGE sql STABLE
    AS $$
  select 
    coalesce(
        nullif(current_setting('request.jwt.claim', true), ''),
        nullif(current_setting('request.jwt.claims', true), '')
    )::jsonb
$$;


ALTER FUNCTION auth.jwt() OWNER TO supabase_auth_admin;

--
-- Name: role(); Type: FUNCTION; Schema: auth; Owner: supabase_auth_admin
--

CREATE FUNCTION auth.role() RETURNS text
    LANGUAGE sql STABLE
    AS $$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.role', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'role')
  )::text
$$;


ALTER FUNCTION auth.role() OWNER TO supabase_auth_admin;

--
-- Name: FUNCTION role(); Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON FUNCTION auth.role() IS 'Deprecated. Use auth.jwt() -> ''role'' instead.';


--
-- Name: uid(); Type: FUNCTION; Schema: auth; Owner: supabase_auth_admin
--

CREATE FUNCTION auth.uid() RETURNS uuid
    LANGUAGE sql STABLE
    AS $$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.sub', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'sub')
  )::uuid
$$;


ALTER FUNCTION auth.uid() OWNER TO supabase_auth_admin;

--
-- Name: FUNCTION uid(); Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON FUNCTION auth.uid() IS 'Deprecated. Use auth.jwt() -> ''sub'' instead.';


--
-- Name: grant_pg_cron_access(); Type: FUNCTION; Schema: extensions; Owner: postgres
--

CREATE FUNCTION extensions.grant_pg_cron_access() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  IF EXISTS (
    SELECT
    FROM pg_event_trigger_ddl_commands() AS ev
    JOIN pg_extension AS ext
    ON ev.objid = ext.oid
    WHERE ext.extname = 'pg_cron'
  )
  THEN
    grant usage on schema cron to postgres with grant option;

    alter default privileges in schema cron grant all on tables to postgres with grant option;
    alter default privileges in schema cron grant all on functions to postgres with grant option;
    alter default privileges in schema cron grant all on sequences to postgres with grant option;

    alter default privileges for user supabase_admin in schema cron grant all
        on sequences to postgres with grant option;
    alter default privileges for user supabase_admin in schema cron grant all
        on tables to postgres with grant option;
    alter default privileges for user supabase_admin in schema cron grant all
        on functions to postgres with grant option;

    grant all privileges on all tables in schema cron to postgres with grant option;
    revoke all on table cron.job from postgres;
    grant select on table cron.job to postgres with grant option;
  END IF;
END;
$$;


ALTER FUNCTION extensions.grant_pg_cron_access() OWNER TO postgres;

--
-- Name: FUNCTION grant_pg_cron_access(); Type: COMMENT; Schema: extensions; Owner: postgres
--

COMMENT ON FUNCTION extensions.grant_pg_cron_access() IS 'Grants access to pg_cron';


--
-- Name: grant_pg_graphql_access(); Type: FUNCTION; Schema: extensions; Owner: supabase_admin
--

CREATE FUNCTION extensions.grant_pg_graphql_access() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $_$
DECLARE
    func_is_graphql_resolve bool;
BEGIN
    func_is_graphql_resolve = (
        SELECT n.proname = 'resolve'
        FROM pg_event_trigger_ddl_commands() AS ev
        LEFT JOIN pg_catalog.pg_proc AS n
        ON ev.objid = n.oid
    );

    IF func_is_graphql_resolve
    THEN
        -- Update public wrapper to pass all arguments through to the pg_graphql resolve func
        DROP FUNCTION IF EXISTS graphql_public.graphql;
        create or replace function graphql_public.graphql(
            "operationName" text default null,
            query text default null,
            variables jsonb default null,
            extensions jsonb default null
        )
            returns jsonb
            language sql
        as $$
            select graphql.resolve(
                query := query,
                variables := coalesce(variables, '{}'),
                "operationName" := "operationName",
                extensions := extensions
            );
        $$;

        -- This hook executes when `graphql.resolve` is created. That is not necessarily the last
        -- function in the extension so we need to grant permissions on existing entities AND
        -- update default permissions to any others that are created after `graphql.resolve`
        grant usage on schema graphql to postgres, anon, authenticated, service_role;
        grant select on all tables in schema graphql to postgres, anon, authenticated, service_role;
        grant execute on all functions in schema graphql to postgres, anon, authenticated, service_role;
        grant all on all sequences in schema graphql to postgres, anon, authenticated, service_role;
        alter default privileges in schema graphql grant all on tables to postgres, anon, authenticated, service_role;
        alter default privileges in schema graphql grant all on functions to postgres, anon, authenticated, service_role;
        alter default privileges in schema graphql grant all on sequences to postgres, anon, authenticated, service_role;

        -- Allow postgres role to allow granting usage on graphql and graphql_public schemas to custom roles
        grant usage on schema graphql_public to postgres with grant option;
        grant usage on schema graphql to postgres with grant option;
    END IF;

END;
$_$;


ALTER FUNCTION extensions.grant_pg_graphql_access() OWNER TO supabase_admin;

--
-- Name: FUNCTION grant_pg_graphql_access(); Type: COMMENT; Schema: extensions; Owner: supabase_admin
--

COMMENT ON FUNCTION extensions.grant_pg_graphql_access() IS 'Grants access to pg_graphql';


--
-- Name: grant_pg_net_access(); Type: FUNCTION; Schema: extensions; Owner: postgres
--

CREATE FUNCTION extensions.grant_pg_net_access() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  IF EXISTS (
    SELECT 1
    FROM pg_event_trigger_ddl_commands() AS ev
    JOIN pg_extension AS ext
    ON ev.objid = ext.oid
    WHERE ext.extname = 'pg_net'
  )
  THEN
    IF NOT EXISTS (
      SELECT 1
      FROM pg_roles
      WHERE rolname = 'supabase_functions_admin'
    )
    THEN
      CREATE USER supabase_functions_admin NOINHERIT CREATEROLE LOGIN NOREPLICATION;
    END IF;

    GRANT USAGE ON SCHEMA net TO supabase_functions_admin, postgres, anon, authenticated, service_role;

    IF EXISTS (
      SELECT FROM pg_extension
      WHERE extname = 'pg_net'
      -- all versions in use on existing projects as of 2025-02-20
      -- version 0.12.0 onwards don't need these applied
      AND extversion IN ('0.2', '0.6', '0.7', '0.7.1', '0.8', '0.10.0', '0.11.0')
    ) THEN
      ALTER function net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) SECURITY DEFINER;
      ALTER function net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) SECURITY DEFINER;

      ALTER function net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) SET search_path = net;
      ALTER function net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) SET search_path = net;

      REVOKE ALL ON FUNCTION net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) FROM PUBLIC;
      REVOKE ALL ON FUNCTION net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) FROM PUBLIC;

      GRANT EXECUTE ON FUNCTION net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) TO supabase_functions_admin, postgres, anon, authenticated, service_role;
      GRANT EXECUTE ON FUNCTION net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) TO supabase_functions_admin, postgres, anon, authenticated, service_role;
    END IF;
  END IF;
END;
$$;


ALTER FUNCTION extensions.grant_pg_net_access() OWNER TO postgres;

--
-- Name: FUNCTION grant_pg_net_access(); Type: COMMENT; Schema: extensions; Owner: postgres
--

COMMENT ON FUNCTION extensions.grant_pg_net_access() IS 'Grants access to pg_net';


--
-- Name: pgrst_ddl_watch(); Type: FUNCTION; Schema: extensions; Owner: supabase_admin
--

CREATE FUNCTION extensions.pgrst_ddl_watch() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  cmd record;
BEGIN
  FOR cmd IN SELECT * FROM pg_event_trigger_ddl_commands()
  LOOP
    IF cmd.command_tag IN (
      'CREATE SCHEMA', 'ALTER SCHEMA'
    , 'CREATE TABLE', 'CREATE TABLE AS', 'SELECT INTO', 'ALTER TABLE'
    , 'CREATE FOREIGN TABLE', 'ALTER FOREIGN TABLE'
    , 'CREATE VIEW', 'ALTER VIEW'
    , 'CREATE MATERIALIZED VIEW', 'ALTER MATERIALIZED VIEW'
    , 'CREATE FUNCTION', 'ALTER FUNCTION'
    , 'CREATE TRIGGER'
    , 'CREATE TYPE', 'ALTER TYPE'
    , 'CREATE RULE'
    , 'COMMENT'
    )
    -- don't notify in case of CREATE TEMP table or other objects created on pg_temp
    AND cmd.schema_name is distinct from 'pg_temp'
    THEN
      NOTIFY pgrst, 'reload schema';
    END IF;
  END LOOP;
END; $$;


ALTER FUNCTION extensions.pgrst_ddl_watch() OWNER TO supabase_admin;

--
-- Name: pgrst_drop_watch(); Type: FUNCTION; Schema: extensions; Owner: supabase_admin
--

CREATE FUNCTION extensions.pgrst_drop_watch() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  obj record;
BEGIN
  FOR obj IN SELECT * FROM pg_event_trigger_dropped_objects()
  LOOP
    IF obj.object_type IN (
      'schema'
    , 'table'
    , 'foreign table'
    , 'view'
    , 'materialized view'
    , 'function'
    , 'trigger'
    , 'type'
    , 'rule'
    )
    AND obj.is_temporary IS false -- no pg_temp objects
    THEN
      NOTIFY pgrst, 'reload schema';
    END IF;
  END LOOP;
END; $$;


ALTER FUNCTION extensions.pgrst_drop_watch() OWNER TO supabase_admin;

--
-- Name: set_graphql_placeholder(); Type: FUNCTION; Schema: extensions; Owner: supabase_admin
--

CREATE FUNCTION extensions.set_graphql_placeholder() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $_$
    DECLARE
    graphql_is_dropped bool;
    BEGIN
    graphql_is_dropped = (
        SELECT ev.schema_name = 'graphql_public'
        FROM pg_event_trigger_dropped_objects() AS ev
        WHERE ev.schema_name = 'graphql_public'
    );

    IF graphql_is_dropped
    THEN
        create or replace function graphql_public.graphql(
            "operationName" text default null,
            query text default null,
            variables jsonb default null,
            extensions jsonb default null
        )
            returns jsonb
            language plpgsql
        as $$
            DECLARE
                server_version float;
            BEGIN
                server_version = (SELECT (SPLIT_PART((select version()), ' ', 2))::float);

                IF server_version >= 14 THEN
                    RETURN jsonb_build_object(
                        'errors', jsonb_build_array(
                            jsonb_build_object(
                                'message', 'pg_graphql extension is not enabled.'
                            )
                        )
                    );
                ELSE
                    RETURN jsonb_build_object(
                        'errors', jsonb_build_array(
                            jsonb_build_object(
                                'message', 'pg_graphql is only available on projects running Postgres 14 onwards.'
                            )
                        )
                    );
                END IF;
            END;
        $$;
    END IF;

    END;
$_$;


ALTER FUNCTION extensions.set_graphql_placeholder() OWNER TO supabase_admin;

--
-- Name: FUNCTION set_graphql_placeholder(); Type: COMMENT; Schema: extensions; Owner: supabase_admin
--

COMMENT ON FUNCTION extensions.set_graphql_placeholder() IS 'Reintroduces placeholder function for graphql_public.graphql';


--
-- Name: get_auth(text); Type: FUNCTION; Schema: pgbouncer; Owner: supabase_admin
--

CREATE FUNCTION pgbouncer.get_auth(p_usename text) RETURNS TABLE(username text, password text)
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
    RAISE WARNING 'PgBouncer auth request: %', p_usename;

    RETURN QUERY
    SELECT usename::TEXT, passwd::TEXT FROM pg_catalog.pg_shadow
    WHERE usename = p_usename;
END;
$$;


ALTER FUNCTION pgbouncer.get_auth(p_usename text) OWNER TO supabase_admin;

--
-- Name: add_author_daily_quota_for_prompt_usage(uuid, uuid, text); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.add_author_daily_quota_for_prompt_usage(user_uuid_param uuid, prompt_id_param uuid, access_password text) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $_$
DECLARE
  author_email TEXT;
  author_user_id UUID;
  uuid_string TEXT;
  prompt_uuid_string TEXT;
BEGIN
  -- 密码校验
  IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
    RAISE EXCEPTION 'Invalid access password';
  END IF;
  
  -- 参数验证
  IF user_uuid_param IS NULL THEN
    RAISE EXCEPTION 'user_uuid_param cannot be null';
  END IF;
  
  IF prompt_id_param IS NULL THEN
    RAISE EXCEPTION 'prompt_id_param cannot be null';
  END IF;
  
  -- UUID格式验证
  uuid_string := user_uuid_param::text;
  IF LENGTH(uuid_string) != 36 OR 
     uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid user UUID format';
  END IF;
  
  prompt_uuid_string := prompt_id_param::text;
  IF LENGTH(prompt_uuid_string) != 36 OR 
     prompt_uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid prompt UUID format';
  END IF;
  
  -- 获取提示词作者的邮箱地址（因为created_by字段存储的是邮箱）
  SELECT created_by INTO author_email
  FROM "propmt-zhanshi"
  WHERE id = prompt_id_param;
  
  -- 如果提示词不存在
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- 通过邮箱地址查找作者的UUID
  SELECT user_id INTO author_user_id
  FROM "membership-true"
  WHERE email = author_email AND is_verified = TRUE;
  
  -- 如果作者不存在或未认证
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- 如果是作者自己使用自己的提示词，不给奖励
  IF author_user_id = user_uuid_param THEN
    RETURN FALSE;
  END IF;
  
  -- 为作者添加1次奖励额度（操作true表，触发器会自动同步到look表）
  UPDATE "membership-true"
  SET 
    reward_quota = COALESCE(reward_quota, 0) + 1,
    updated_at = NOW()
  WHERE user_id = author_user_id AND is_verified = TRUE;
  
  -- 检查是否更新成功
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  RETURN TRUE;
END;
$_$;


ALTER FUNCTION public.add_author_daily_quota_for_prompt_usage(user_uuid_param uuid, prompt_id_param uuid, access_password text) OWNER TO postgres;

--
-- Name: add_reward_quota(uuid, integer, text); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.add_reward_quota(user_uuid uuid, reward_amount integer, access_password text) RETURNS boolean
    LANGUAGE plpgsql
    AS $_$
DECLARE
  uuid_string TEXT;
BEGIN
  -- 密码校验
  IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
    RAISE EXCEPTION 'Invalid access password';
  END IF;
  
  -- 参数验证
  IF user_uuid IS NULL THEN
    RAISE EXCEPTION 'user_uuid cannot be null';
  END IF;
  
  -- 严格的UUID格式验证
  uuid_string := user_uuid::text;
  IF LENGTH(uuid_string) != 36 OR 
     uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid UUID format';
  END IF;
  
  IF reward_amount IS NULL OR reward_amount <= 0 THEN
    RAISE EXCEPTION 'reward_amount must be positive';
  END IF;
  
  -- 防止恶意大量添加
  IF reward_amount > 50 THEN
    RAISE EXCEPTION 'reward_amount too large (max: 50)';
  END IF;
  
  -- 添加奖励额度（操作true表，触发器会自动同步到look表）
  UPDATE "membership-true"
  SET 
    reward_quota = COALESCE(reward_quota, 0) + reward_amount,
    updated_at = NOW()
  WHERE user_id = user_uuid AND is_verified = TRUE;
  
  -- 检查是否更新成功
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  RETURN TRUE;
END;
$_$;


ALTER FUNCTION public.add_reward_quota(user_uuid uuid, reward_amount integer, access_password text) OWNER TO postgres;

--
-- Name: create_user_membership(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.create_user_membership() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  -- 为新注册用户创建免费会员记录
  BEGIN
    INSERT INTO public."membership-true" (user_id, email, membership_level, word_count_used, word_count_limit)
    VALUES (NEW.id, NEW.email, '免费', 0, 0);
  EXCEPTION 
    WHEN unique_violation THEN
      -- 如果邮箱已存在，忽略错误
      NULL;
  END;
  
  RETURN NEW;
END;
$$;


ALTER FUNCTION public.create_user_membership() OWNER TO postgres;

--
-- Name: create_user_prompt(text, text, text, text, text, text, text); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.create_user_prompt(title_param text, description_param text, type_param text, content_param text, user_email_param text, user_display_name_param text, access_password text) RETURNS json
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
    new_prompt_id UUID;
    result JSON;
BEGIN
    -- 密码校验
    IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
        RETURN json_build_object(
            'success', false,
            'error', '访问密码错误',
            'code', 'INVALID_PASSWORD'
        );
    END IF;
    
    -- 参数验证
    IF title_param IS NULL OR LENGTH(TRIM(title_param)) = 0 THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词标题不能为空',
            'code', 'INVALID_TITLE'
        );
    END IF;
    
    IF type_param IS NULL OR LENGTH(TRIM(type_param)) = 0 THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词类型不能为空',
            'code', 'INVALID_TYPE'
        );
    END IF;
    
    IF content_param IS NULL OR LENGTH(TRIM(content_param)) = 0 THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词内容不能为空',
            'code', 'INVALID_CONTENT'
        );
    END IF;
    
    IF user_email_param IS NULL OR LENGTH(TRIM(user_email_param)) = 0 THEN
        RETURN json_build_object(
            'success', false,
            'error', '用户邮箱不能为空',
            'code', 'INVALID_USER_EMAIL'
        );
    END IF;
    
    IF user_display_name_param IS NULL OR LENGTH(TRIM(user_display_name_param)) = 0 THEN
        RETURN json_build_object(
            'success', false,
            'error', '用户显示名称不能为空',
            'code', 'INVALID_USER_DISPLAY_NAME'
        );
    END IF;
    
    -- 内容长度限制（一万字）
    IF LENGTH(content_param) > 10000 THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词内容不能超过10000字',
            'code', 'CONTENT_TOO_LONG'
        );
    END IF;
    
    -- 生成新的UUID
    new_prompt_id := gen_random_uuid();
    
    -- 插入到展示表
    INSERT INTO "propmt-zhanshi" (
        id, title, description, category, type, created_by, author_display_id, created_at, updated_at
    ) VALUES (
        new_prompt_id, 
        TRIM(title_param), 
        TRIM(description_param), 
        'userprompt', 
        TRIM(type_param), 
        TRIM(user_email_param),
        TRIM(user_display_name_param),
        NOW(), 
        NOW()
    );
    
    -- 插入到内容表（使用prompt_id作为主键，不再生成独立的id）
    INSERT INTO "propmt-neirong" (
        prompt_id, content, title, author_display_id, created_at, updated_at
    ) VALUES (
        new_prompt_id,
        TRIM(content_param),
        TRIM(title_param),
        TRIM(user_display_name_param),
        NOW(),
        NOW()
    );
    
    -- 构建返回结果
    result := json_build_object(
        'success', true,
        'data', json_build_object(
            'id', new_prompt_id,
            'title', TRIM(title_param),
            'description', TRIM(description_param),
            'type', TRIM(type_param),
            'category', 'userprompt',
            'author_display_id', TRIM(user_display_name_param)
        )
    );
    
    RETURN result;
END;
$$;


ALTER FUNCTION public.create_user_prompt(title_param text, description_param text, type_param text, content_param text, user_email_param text, user_display_name_param text, access_password text) OWNER TO postgres;

--
-- Name: deduct_daily_free_quota(uuid, integer, text); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.deduct_daily_free_quota(user_uuid uuid, deduct_amount integer, access_password text) RETURNS json
    LANGUAGE plpgsql SECURITY DEFINER
    AS $_$
DECLARE
  current_daily_used INTEGER;
  current_daily_quota INTEGER;
  current_reward_used INTEGER;
  current_reward_quota INTEGER;
  last_reset DATE;
  uuid_string TEXT;
  daily_available INTEGER;
  reward_available INTEGER;
  daily_deduct INTEGER := 0;
  reward_deduct INTEGER := 0;
  result JSON;
BEGIN
  -- 密码校验
  IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
    RAISE EXCEPTION 'Invalid access password';
  END IF;
  
  -- 参数验证
  IF user_uuid IS NULL THEN
    RAISE EXCEPTION 'user_uuid cannot be null';
  END IF;
  
  -- 严格的UUID格式验证
  uuid_string := user_uuid::text;
  IF LENGTH(uuid_string) != 36 OR 
     uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid UUID format';
  END IF;
  
  IF deduct_amount IS NULL OR deduct_amount <= 0 THEN
    RAISE EXCEPTION 'deduct_amount must be positive';
  END IF;
  
  -- 防止恶意大量扣除
  IF deduct_amount > 100 THEN
    RAISE EXCEPTION 'deduct_amount too large (max: 100)';
  END IF;
  
  -- 获取当前额度使用情况（从true表查询）
  SELECT daily_free_used, daily_free_quota, reward_used, reward_quota, last_free_reset_date
  INTO current_daily_used, current_daily_quota, current_reward_used, current_reward_quota, last_reset
  FROM "membership-true"
  WHERE user_id = user_uuid AND is_verified = TRUE;
  
  -- 如果用户不存在或未认证
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'message', 'User not found or not verified',
      'used_daily', false,
      'used_reward', false
    );
  END IF;
  
  -- 检查是否需要重置每日额度
  IF last_reset < CURRENT_DATE THEN
    UPDATE "membership-true"
    SET 
      daily_free_used = 0,
      reward_used = 0,  -- 奖励额度也每日重置
      last_free_reset_date = CURRENT_DATE,
      updated_at = NOW()
    WHERE user_id = user_uuid;
    current_daily_used := 0;
    current_reward_used := 0;
  END IF;
  
  -- 计算可用额度
  daily_available := GREATEST(0, current_daily_quota - current_daily_used);
  reward_available := GREATEST(0, current_reward_quota - current_reward_used);
  
  -- 检查总额度是否足够
  IF daily_available + reward_available < deduct_amount THEN
    RETURN json_build_object(
      'success', false,
      'message', 'Insufficient quota',
      'daily_available', daily_available,
      'reward_available', reward_available,
      'used_daily', false,
      'used_reward', false
    );
  END IF;
  
  -- 优先扣除每日免费额度
  IF daily_available > 0 THEN
    daily_deduct := LEAST(daily_available, deduct_amount);
    deduct_amount := deduct_amount - daily_deduct;
  END IF;
  
  -- 如果还有剩余，扣除奖励额度
  IF deduct_amount > 0 AND reward_available > 0 THEN
    reward_deduct := LEAST(reward_available, deduct_amount);
  END IF;
  
  -- 执行扣除操作（操作true表，触发器会自动同步到look表）
  UPDATE "membership-true"
  SET 
    daily_free_used = current_daily_used + daily_deduct,
    reward_used = current_reward_used + reward_deduct,
    updated_at = NOW()
  WHERE user_id = user_uuid;
  
  -- 返回扣费结果
  RETURN json_build_object(
    'success', true,
    'message', 'Deduction successful',
    'daily_deducted', daily_deduct,
    'reward_deducted', reward_deduct,
    'used_daily', daily_deduct > 0,
    'used_reward', reward_deduct > 0
  );
END;
$_$;


ALTER FUNCTION public.deduct_daily_free_quota(user_uuid uuid, deduct_amount integer, access_password text) OWNER TO postgres;

--
-- Name: deduct_user_word_count(uuid, integer, text); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.deduct_user_word_count(user_uuid uuid, deduct_amount integer, access_password text) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $_$
DECLARE
    current_used INTEGER;
    current_limit INTEGER;
    uuid_string TEXT;
BEGIN
    -- 密码校验
    IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
        RAISE EXCEPTION 'Invalid access password';
    END IF;
    
    -- 参数验证
    IF user_uuid IS NULL THEN
        RAISE EXCEPTION 'user_uuid cannot be null';
    END IF;
    
    -- 严格的UUID格式验证
    uuid_string := user_uuid::text;
    IF LENGTH(uuid_string) != 36 OR 
       uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
        RAISE EXCEPTION 'Invalid UUID format';
    END IF;
    
    IF deduct_amount IS NULL OR deduct_amount <= 0 THEN
        RAISE EXCEPTION 'deduct_amount must be positive';
    END IF;
    
    -- 防止恶意大量扣除
    IF deduct_amount > 1000000 THEN
        RAISE EXCEPTION 'deduct_amount too large (max: 1000000)';
    END IF;
    
    -- 获取当前用户的字数使用情况
    SELECT word_count_used, word_count_limit
    INTO current_used, current_limit
    FROM "membership-true"
    WHERE user_id = user_uuid AND is_verified = TRUE;
    
    -- 如果用户不存在或未认证
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- 检查余额是否足够
    IF current_used + deduct_amount > current_limit THEN
        RETURN FALSE;
    END IF;
    
    -- 扣除字数
    UPDATE "membership-true"
    SET 
        word_count_used = current_used + deduct_amount,
        updated_at = NOW()
    WHERE user_id = user_uuid;
    
    RETURN TRUE;
END;
$_$;


ALTER FUNCTION public.deduct_user_word_count(user_uuid uuid, deduct_amount integer, access_password text) OWNER TO postgres;

--
-- Name: delete_user_prompt(uuid, text, text); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.delete_user_prompt(prompt_id_param uuid, user_email_param text, access_password text DEFAULT 'PROMPT_ACCESS_2024_SECURE_KEY'::text) RETURNS json
    LANGUAGE plpgsql SECURITY DEFINER
    AS $_$
DECLARE
    prompt_record RECORD;
    result JSON;
    uuid_string TEXT;
BEGIN
    -- 密码校验
    IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
        RETURN json_build_object(
            'success', false,
            'error', '访问密码错误',
            'code', 'INVALID_PASSWORD'
        );
    END IF;
    
    -- UUID格式验证
    IF prompt_id_param IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', '无效的UUID格式',
            'code', 'INVALID_UUID'
        );
    END IF;
    
    uuid_string := prompt_id_param::text;
    IF LENGTH(uuid_string) != 36 OR 
       uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
        RETURN json_build_object(
            'success', false,
            'error', '无效的UUID格式',
            'code', 'INVALID_UUID'
        );
    END IF;
    
    -- 参数验证
    IF user_email_param IS NULL OR LENGTH(TRIM(user_email_param)) = 0 THEN
        RETURN json_build_object(
            'success', false,
            'error', '用户邮箱不能为空',
            'code', 'INVALID_USER_EMAIL'
        );
    END IF;
    
    -- 检查提示词是否存在且用户有权限
    SELECT id, title, created_by INTO prompt_record
    FROM "propmt-zhanshi"
    WHERE id = prompt_id_param AND created_by = TRIM(user_email_param);
    
    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词不存在或无权限删除',
            'code', 'PERMISSION_DENIED'
        );
    END IF;
    
    -- 删除内容表记录（使用prompt_id作为主键）
    DELETE FROM "propmt-neirong" WHERE prompt_id = prompt_id_param;
    
    -- 删除展示表记录
    DELETE FROM "propmt-zhanshi" WHERE id = prompt_id_param;
    
    result := json_build_object(
        'success', true,
        'data', json_build_object(
            'id', prompt_id_param,
            'title', prompt_record.title,
            'message', '提示词删除成功'
        )
    );
    
    RETURN result;
END;
$_$;


ALTER FUNCTION public.delete_user_prompt(prompt_id_param uuid, user_email_param text, access_password text) OWNER TO postgres;

--
-- Name: get_prompt_content(uuid, text, text); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.get_prompt_content(prompt_id_param uuid, access_password text, user_email_param text DEFAULT NULL::text) RETURNS json
    LANGUAGE plpgsql SECURITY DEFINER
    AS $_$
DECLARE
    prompt_record RECORD;
    content_record RECORD;
    result JSON;
    uuid_string TEXT;
BEGIN
    -- 密码校验
    IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
        RETURN json_build_object(
            'success', false,
            'error', '访问密码错误',
            'code', 'INVALID_PASSWORD'
        );
    END IF;
    
    -- 严格的UUID格式验证
    IF prompt_id_param IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', '无效的UUID格式',
            'code', 'INVALID_UUID'
        );
    END IF;
    
    -- 将UUID转换为字符串进行格式检查
    uuid_string := prompt_id_param::text;
    
    -- 检查UUID字符串格式
    IF LENGTH(uuid_string) != 36 OR 
       uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
        RETURN json_build_object(
            'success', false,
            'error', '无效的UUID格式',
            'code', 'INVALID_UUID'
        );
    END IF;
    
    -- 检查提示词是否存在
    SELECT id, title, description, type, category, created_by
    INTO prompt_record
    FROM "propmt-zhanshi"
    WHERE id = prompt_id_param;
    
    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词不存在',
            'code', 'PROMPT_NOT_FOUND'
        );
    END IF;
    
    -- 修改权限验证：允许认证用户使用他人的提示词
    IF prompt_record.category = 'userprompt' AND user_email_param IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', '需要登录才能使用用户提示词',
            'code', 'LOGIN_REQUIRED'
        );
    END IF;
    
    -- 获取提示词内容并增加使用次数（直接使用prompt_id作为主键查询）
    UPDATE "propmt-neirong" 
    SET usage_count = usage_count + 1,
        updated_at = NOW()
    WHERE prompt_id = prompt_id_param
    RETURNING content, title, usage_count INTO content_record;
    
    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词内容不存在',
            'code', 'CONTENT_NOT_FOUND'
        );
    END IF;
    
    -- 构建返回结果
    result := json_build_object(
        'success', true,
        'data', json_build_object(
            'id', prompt_record.id,
            'title', content_record.title,
            'description', prompt_record.description,
            'type', prompt_record.type,
            'category', prompt_record.category,
            'content', content_record.content,
            'created_by', prompt_record.created_by,
            'usage_count', content_record.usage_count
        )
    );
    
    RETURN result;
END;
$_$;


ALTER FUNCTION public.get_prompt_content(prompt_id_param uuid, access_password text, user_email_param text) OWNER TO postgres;

--
-- Name: get_user_quota_info(uuid, text); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.get_user_quota_info(user_uuid uuid, access_password text) RETURNS json
    LANGUAGE plpgsql
    AS $_$
DECLARE
  user_info RECORD;
  uuid_string TEXT;
  daily_available INTEGER;
  reward_available INTEGER;
BEGIN
  -- 密码校验
  IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
    RAISE EXCEPTION 'Invalid access password';
  END IF;
  
  -- 参数验证
  IF user_uuid IS NULL THEN
    RAISE EXCEPTION 'user_uuid cannot be null';
  END IF;
  
  -- UUID格式验证
  uuid_string := user_uuid::text;
  IF LENGTH(uuid_string) != 36 OR 
     uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid UUID format';
  END IF;
  
  -- 获取用户额度信息（从look表查询）
  SELECT 
    daily_free_quota,
    daily_free_used,
    reward_quota,
    reward_used,
    last_free_reset_date,
    is_verified
  INTO user_info
  FROM "membership-look"
  WHERE user_id = user_uuid;
  
  -- 如果用户不存在
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'message', 'User not found'
    );
  END IF;
  
  -- 检查是否需要重置（但不实际重置，只计算）
  IF user_info.last_free_reset_date < CURRENT_DATE THEN
    daily_available := user_info.daily_free_quota;
    reward_available := user_info.reward_quota;
  ELSE
    daily_available := GREATEST(0, user_info.daily_free_quota - user_info.daily_free_used);
    reward_available := GREATEST(0, user_info.reward_quota - user_info.reward_used);
  END IF;
  
  -- 返回完整信息
  RETURN json_build_object(
    'success', true,
    'is_verified', user_info.is_verified,
    'daily_free_quota', user_info.daily_free_quota,
    'daily_free_used', CASE WHEN user_info.last_free_reset_date < CURRENT_DATE THEN 0 ELSE user_info.daily_free_used END,
    'daily_free_remaining', daily_available,
    'reward_quota', user_info.reward_quota,
    'reward_used', CASE WHEN user_info.last_free_reset_date < CURRENT_DATE THEN 0 ELSE user_info.reward_used END,
    'reward_remaining', reward_available,
    'last_reset_date', user_info.last_free_reset_date,
    'needs_reset', user_info.last_free_reset_date < CURRENT_DATE
  );
END;
$_$;


ALTER FUNCTION public.get_user_quota_info(user_uuid uuid, access_password text) OWNER TO postgres;

--
-- Name: increment_prompt_usage(uuid, text); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.increment_prompt_usage(prompt_id_param uuid, access_password text) RETURNS json
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
    result JSON;
    updated_count INTEGER;
BEGIN
    -- 密码校验
    IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
        RETURN json_build_object(
            'success', false,
            'error', '访问密码错误',
            'code', 'INVALID_PASSWORD'
        );
    END IF;
    
    -- UUID格式验证
    IF prompt_id_param IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词ID不能为空',
            'code', 'INVALID_PROMPT_ID'
        );
    END IF;
    
    -- 增加使用次数
    UPDATE "propmt-neirong" 
    SET usage_count = usage_count + 1,
        updated_at = NOW()
    WHERE prompt_id = prompt_id_param
    RETURNING usage_count INTO updated_count;
    
    -- 检查是否找到记录
    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词内容不存在',
            'code', 'CONTENT_NOT_FOUND'
        );
    END IF;
    
    -- 构建返回结果
    result := json_build_object(
        'success', true,
        'data', json_build_object(
            'prompt_id', prompt_id_param,
            'usage_count', updated_count
        )
    );
    
    RETURN result;
END;
$$;


ALTER FUNCTION public.increment_prompt_usage(prompt_id_param uuid, access_password text) OWNER TO postgres;

--
-- Name: reset_all_user_quotas(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.reset_all_user_quotas() RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
  reset_count INTEGER := 0;
BEGIN
  -- 重置所有用户的免费额度和奖励额度
  UPDATE "membership-true"
  SET 
    daily_free_used = 0,
    daily_free_quota = 25,
    reward_quota = 0,
    reward_used = 0,
    last_free_reset_date = CURRENT_DATE,
    updated_at = NOW();
  
  GET DIAGNOSTICS reset_count = ROW_COUNT;
  
  -- 记录操作日志
  RAISE NOTICE '重置了 % 个用户的额度配置', reset_count;
  
  RETURN reset_count;
END;
$$;


ALTER FUNCTION public.reset_all_user_quotas() OWNER TO postgres;

--
-- Name: set_initial_title(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.set_initial_title() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    prompt_title TEXT;
BEGIN
    -- 获取对应的提示词标题
    SELECT title INTO prompt_title
    FROM "propmt-zhanshi"
    WHERE id = NEW.prompt_id;
    
    -- 如果标题为空，设置为获取到的标题
    IF NEW.title = '' OR NEW.title IS NULL THEN
        NEW.title := COALESCE(prompt_title, '');
    END IF;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.set_initial_title() OWNER TO postgres;

--
-- Name: set_user_verification_status(uuid, boolean, integer); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.set_user_verification_status(user_uuid uuid, verified_status boolean, free_quota integer DEFAULT 50) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $_$
BEGIN
  -- 参数验证
  IF user_uuid IS NULL THEN
    RAISE EXCEPTION 'user_uuid cannot be null';
  END IF;
  
  -- 验证UUID格式
  IF NOT (user_uuid::text ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$') THEN
    RAISE EXCEPTION 'Invalid UUID format';
  END IF;
  
  -- 更新认证状态
  UPDATE "membership-true"
  SET 
    is_verified = verified_status,
    daily_free_quota = CASE WHEN verified_status THEN free_quota ELSE 0 END,
    daily_free_used = 0,
    last_free_reset_date = CURRENT_DATE,
    updated_at = NOW()
  WHERE user_id = user_uuid;
  
  -- 检查是否更新成功
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  RETURN TRUE;
END;
$_$;


ALTER FUNCTION public.set_user_verification_status(user_uuid uuid, verified_status boolean, free_quota integer) OWNER TO postgres;

--
-- Name: sync_membership_to_look(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.sync_membership_to_look() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    INSERT INTO public."membership-look" VALUES (NEW.*)
    ON CONFLICT (id) DO NOTHING;
    RETURN NEW;
  ELSIF TG_OP = 'UPDATE' THEN
    UPDATE public."membership-look" SET 
      email = NEW.email,
      membership_level = NEW.membership_level,
      word_count_limit = NEW.word_count_limit,
      word_count_used = NEW.word_count_used,
      created_at = NEW.created_at,
      updated_at = NEW.updated_at,
      user_id = NEW.user_id,
      is_verified = NEW.is_verified,
      daily_free_quota = NEW.daily_free_quota,
      daily_free_used = NEW.daily_free_used,
      last_free_reset_date = NEW.last_free_reset_date,
      reward_quota = NEW.reward_quota,
      reward_used = NEW.reward_used
    WHERE id = NEW.id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    DELETE FROM public."membership-look" WHERE id = OLD.id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$;


ALTER FUNCTION public.sync_membership_to_look() OWNER TO postgres;

--
-- Name: sync_prompt_title(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.sync_prompt_title() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- 当 propmt-zhanshi 表的标题更新时，同步更新 propmt-neirong 表
    IF TG_OP = 'UPDATE' AND OLD.title IS DISTINCT FROM NEW.title THEN
        UPDATE "propmt-neirong" 
        SET title = NEW.title,
            updated_at = NOW()
        WHERE prompt_id = NEW.id;
    END IF;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.sync_prompt_title() OWNER TO postgres;

--
-- Name: sync_usage_count_to_zhanshi(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.sync_usage_count_to_zhanshi() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- 当propmt-neirong表的usage_count字段更新时，同步更新propmt-zhanshi表
    IF TG_OP = 'UPDATE' AND OLD.usage_count IS DISTINCT FROM NEW.usage_count THEN
        UPDATE "propmt-zhanshi" 
        SET usage_count = NEW.usage_count,
            updated_at = NOW()
        WHERE id = NEW.prompt_id;
    END IF;
    
    -- 当插入新记录时，也要同步usage_count（虽然通常是0）
    IF TG_OP = 'INSERT' THEN
        UPDATE "propmt-zhanshi" 
        SET usage_count = NEW.usage_count,
            updated_at = NOW()
        WHERE id = NEW.prompt_id;
    END IF;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.sync_usage_count_to_zhanshi() OWNER TO postgres;

--
-- Name: update_user_prompt(uuid, text, text, text, text, text, text); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.update_user_prompt(prompt_id_param uuid, title_param text, description_param text, type_param text, content_param text, user_email_param text, access_password text DEFAULT 'PROMPT_ACCESS_2024_SECURE_KEY'::text) RETURNS json
    LANGUAGE plpgsql SECURITY DEFINER
    AS $_$
DECLARE
    prompt_record RECORD;
    result JSON;
    uuid_string TEXT;
BEGIN
    -- 密码校验
    IF access_password != 'PROMPT_ACCESS_2024_SECURE_KEY' THEN
        RETURN json_build_object(
            'success', false,
            'error', '访问密码错误',
            'code', 'INVALID_PASSWORD'
        );
    END IF;
    
    -- UUID格式验证
    IF prompt_id_param IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', '无效的UUID格式',
            'code', 'INVALID_UUID'
        );
    END IF;
    
    uuid_string := prompt_id_param::text;
    IF LENGTH(uuid_string) != 36 OR 
       uuid_string !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
        RETURN json_build_object(
            'success', false,
            'error', '无效的UUID格式',
            'code', 'INVALID_UUID'
        );
    END IF;
    
    -- 参数验证
    IF title_param IS NULL OR LENGTH(TRIM(title_param)) = 0 THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词标题不能为空',
            'code', 'INVALID_TITLE'
        );
    END IF;
    
    IF content_param IS NULL OR LENGTH(TRIM(content_param)) = 0 THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词内容不能为空',
            'code', 'INVALID_CONTENT'
        );
    END IF;
    
    IF user_email_param IS NULL OR LENGTH(TRIM(user_email_param)) = 0 THEN
        RETURN json_build_object(
            'success', false,
            'error', '用户邮箱不能为空',
            'code', 'INVALID_USER_EMAIL'
        );
    END IF;
    
    -- 内容长度限制
    IF LENGTH(content_param) > 10000 THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词内容不能超过10000字',
            'code', 'CONTENT_TOO_LONG'
        );
    END IF;
    
    -- 检查提示词是否存在且用户有权限
    SELECT id, created_by INTO prompt_record
    FROM "propmt-zhanshi"
    WHERE id = prompt_id_param AND created_by = TRIM(user_email_param);
    
    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', '提示词不存在或无权限修改',
            'code', 'PERMISSION_DENIED'
        );
    END IF;
    
    -- 更新展示表
    UPDATE "propmt-zhanshi" 
    SET 
        title = TRIM(title_param),
        description = TRIM(description_param),
        type = TRIM(type_param),
        updated_at = NOW()
    WHERE id = prompt_id_param;
    
    -- 更新内容表（使用prompt_id作为主键查询）
    UPDATE "propmt-neirong" 
    SET 
        content = TRIM(content_param),
        title = TRIM(title_param),
        updated_at = NOW()
    WHERE prompt_id = prompt_id_param;
    
    result := json_build_object(
        'success', true,
        'data', json_build_object(
            'id', prompt_id_param,
            'title', TRIM(title_param),
            'description', TRIM(description_param),
            'type', TRIM(type_param)
        )
    );
    
    RETURN result;
END;
$_$;


ALTER FUNCTION public.update_user_prompt(prompt_id_param uuid, title_param text, description_param text, type_param text, content_param text, user_email_param text, access_password text) OWNER TO postgres;

--
-- Name: apply_rls(jsonb, integer); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer DEFAULT (1024 * 1024)) RETURNS SETOF realtime.wal_rls
    LANGUAGE plpgsql
    AS $$
declare
-- Regclass of the table e.g. public.notes
entity_ regclass = (quote_ident(wal ->> 'schema') || '.' || quote_ident(wal ->> 'table'))::regclass;

-- I, U, D, T: insert, update ...
action realtime.action = (
    case wal ->> 'action'
        when 'I' then 'INSERT'
        when 'U' then 'UPDATE'
        when 'D' then 'DELETE'
        else 'ERROR'
    end
);

-- Is row level security enabled for the table
is_rls_enabled bool = relrowsecurity from pg_class where oid = entity_;

subscriptions realtime.subscription[] = array_agg(subs)
    from
        realtime.subscription subs
    where
        subs.entity = entity_;

-- Subscription vars
roles regrole[] = array_agg(distinct us.claims_role::text)
    from
        unnest(subscriptions) us;

working_role regrole;
claimed_role regrole;
claims jsonb;

subscription_id uuid;
subscription_has_access bool;
visible_to_subscription_ids uuid[] = '{}';

-- structured info for wal's columns
columns realtime.wal_column[];
-- previous identity values for update/delete
old_columns realtime.wal_column[];

error_record_exceeds_max_size boolean = octet_length(wal::text) > max_record_bytes;

-- Primary jsonb output for record
output jsonb;

begin
perform set_config('role', null, true);

columns =
    array_agg(
        (
            x->>'name',
            x->>'type',
            x->>'typeoid',
            realtime.cast(
                (x->'value') #>> '{}',
                coalesce(
                    (x->>'typeoid')::regtype, -- null when wal2json version <= 2.4
                    (x->>'type')::regtype
                )
            ),
            (pks ->> 'name') is not null,
            true
        )::realtime.wal_column
    )
    from
        jsonb_array_elements(wal -> 'columns') x
        left join jsonb_array_elements(wal -> 'pk') pks
            on (x ->> 'name') = (pks ->> 'name');

old_columns =
    array_agg(
        (
            x->>'name',
            x->>'type',
            x->>'typeoid',
            realtime.cast(
                (x->'value') #>> '{}',
                coalesce(
                    (x->>'typeoid')::regtype, -- null when wal2json version <= 2.4
                    (x->>'type')::regtype
                )
            ),
            (pks ->> 'name') is not null,
            true
        )::realtime.wal_column
    )
    from
        jsonb_array_elements(wal -> 'identity') x
        left join jsonb_array_elements(wal -> 'pk') pks
            on (x ->> 'name') = (pks ->> 'name');

for working_role in select * from unnest(roles) loop

    -- Update `is_selectable` for columns and old_columns
    columns =
        array_agg(
            (
                c.name,
                c.type_name,
                c.type_oid,
                c.value,
                c.is_pkey,
                pg_catalog.has_column_privilege(working_role, entity_, c.name, 'SELECT')
            )::realtime.wal_column
        )
        from
            unnest(columns) c;

    old_columns =
            array_agg(
                (
                    c.name,
                    c.type_name,
                    c.type_oid,
                    c.value,
                    c.is_pkey,
                    pg_catalog.has_column_privilege(working_role, entity_, c.name, 'SELECT')
                )::realtime.wal_column
            )
            from
                unnest(old_columns) c;

    if action <> 'DELETE' and count(1) = 0 from unnest(columns) c where c.is_pkey then
        return next (
            jsonb_build_object(
                'schema', wal ->> 'schema',
                'table', wal ->> 'table',
                'type', action
            ),
            is_rls_enabled,
            -- subscriptions is already filtered by entity
            (select array_agg(s.subscription_id) from unnest(subscriptions) as s where claims_role = working_role),
            array['Error 400: Bad Request, no primary key']
        )::realtime.wal_rls;

    -- The claims role does not have SELECT permission to the primary key of entity
    elsif action <> 'DELETE' and sum(c.is_selectable::int) <> count(1) from unnest(columns) c where c.is_pkey then
        return next (
            jsonb_build_object(
                'schema', wal ->> 'schema',
                'table', wal ->> 'table',
                'type', action
            ),
            is_rls_enabled,
            (select array_agg(s.subscription_id) from unnest(subscriptions) as s where claims_role = working_role),
            array['Error 401: Unauthorized']
        )::realtime.wal_rls;

    else
        output = jsonb_build_object(
            'schema', wal ->> 'schema',
            'table', wal ->> 'table',
            'type', action,
            'commit_timestamp', to_char(
                ((wal ->> 'timestamp')::timestamptz at time zone 'utc'),
                'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'
            ),
            'columns', (
                select
                    jsonb_agg(
                        jsonb_build_object(
                            'name', pa.attname,
                            'type', pt.typname
                        )
                        order by pa.attnum asc
                    )
                from
                    pg_attribute pa
                    join pg_type pt
                        on pa.atttypid = pt.oid
                where
                    attrelid = entity_
                    and attnum > 0
                    and pg_catalog.has_column_privilege(working_role, entity_, pa.attname, 'SELECT')
            )
        )
        -- Add "record" key for insert and update
        || case
            when action in ('INSERT', 'UPDATE') then
                jsonb_build_object(
                    'record',
                    (
                        select
                            jsonb_object_agg(
                                -- if unchanged toast, get column name and value from old record
                                coalesce((c).name, (oc).name),
                                case
                                    when (c).name is null then (oc).value
                                    else (c).value
                                end
                            )
                        from
                            unnest(columns) c
                            full outer join unnest(old_columns) oc
                                on (c).name = (oc).name
                        where
                            coalesce((c).is_selectable, (oc).is_selectable)
                            and ( not error_record_exceeds_max_size or (octet_length((c).value::text) <= 64))
                    )
                )
            else '{}'::jsonb
        end
        -- Add "old_record" key for update and delete
        || case
            when action = 'UPDATE' then
                jsonb_build_object(
                        'old_record',
                        (
                            select jsonb_object_agg((c).name, (c).value)
                            from unnest(old_columns) c
                            where
                                (c).is_selectable
                                and ( not error_record_exceeds_max_size or (octet_length((c).value::text) <= 64))
                        )
                    )
            when action = 'DELETE' then
                jsonb_build_object(
                    'old_record',
                    (
                        select jsonb_object_agg((c).name, (c).value)
                        from unnest(old_columns) c
                        where
                            (c).is_selectable
                            and ( not error_record_exceeds_max_size or (octet_length((c).value::text) <= 64))
                            and ( not is_rls_enabled or (c).is_pkey ) -- if RLS enabled, we can't secure deletes so filter to pkey
                    )
                )
            else '{}'::jsonb
        end;

        -- Create the prepared statement
        if is_rls_enabled and action <> 'DELETE' then
            if (select 1 from pg_prepared_statements where name = 'walrus_rls_stmt' limit 1) > 0 then
                deallocate walrus_rls_stmt;
            end if;
            execute realtime.build_prepared_statement_sql('walrus_rls_stmt', entity_, columns);
        end if;

        visible_to_subscription_ids = '{}';

        for subscription_id, claims in (
                select
                    subs.subscription_id,
                    subs.claims
                from
                    unnest(subscriptions) subs
                where
                    subs.entity = entity_
                    and subs.claims_role = working_role
                    and (
                        realtime.is_visible_through_filters(columns, subs.filters)
                        or (
                          action = 'DELETE'
                          and realtime.is_visible_through_filters(old_columns, subs.filters)
                        )
                    )
        ) loop

            if not is_rls_enabled or action = 'DELETE' then
                visible_to_subscription_ids = visible_to_subscription_ids || subscription_id;
            else
                -- Check if RLS allows the role to see the record
                perform
                    -- Trim leading and trailing quotes from working_role because set_config
                    -- doesn't recognize the role as valid if they are included
                    set_config('role', trim(both '"' from working_role::text), true),
                    set_config('request.jwt.claims', claims::text, true);

                execute 'execute walrus_rls_stmt' into subscription_has_access;

                if subscription_has_access then
                    visible_to_subscription_ids = visible_to_subscription_ids || subscription_id;
                end if;
            end if;
        end loop;

        perform set_config('role', null, true);

        return next (
            output,
            is_rls_enabled,
            visible_to_subscription_ids,
            case
                when error_record_exceeds_max_size then array['Error 413: Payload Too Large']
                else '{}'
            end
        )::realtime.wal_rls;

    end if;
end loop;

perform set_config('role', null, true);
end;
$$;


ALTER FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) OWNER TO supabase_admin;

--
-- Name: broadcast_changes(text, text, text, text, text, record, record, text); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.broadcast_changes(topic_name text, event_name text, operation text, table_name text, table_schema text, new record, old record, level text DEFAULT 'ROW'::text) RETURNS void
    LANGUAGE plpgsql
    AS $$
DECLARE
    -- Declare a variable to hold the JSONB representation of the row
    row_data jsonb := '{}'::jsonb;
BEGIN
    IF level = 'STATEMENT' THEN
        RAISE EXCEPTION 'function can only be triggered for each row, not for each statement';
    END IF;
    -- Check the operation type and handle accordingly
    IF operation = 'INSERT' OR operation = 'UPDATE' OR operation = 'DELETE' THEN
        row_data := jsonb_build_object('old_record', OLD, 'record', NEW, 'operation', operation, 'table', table_name, 'schema', table_schema);
        PERFORM realtime.send (row_data, event_name, topic_name);
    ELSE
        RAISE EXCEPTION 'Unexpected operation type: %', operation;
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Failed to process the row: %', SQLERRM;
END;

$$;


ALTER FUNCTION realtime.broadcast_changes(topic_name text, event_name text, operation text, table_name text, table_schema text, new record, old record, level text) OWNER TO supabase_admin;

--
-- Name: build_prepared_statement_sql(text, regclass, realtime.wal_column[]); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) RETURNS text
    LANGUAGE sql
    AS $$
      /*
      Builds a sql string that, if executed, creates a prepared statement to
      tests retrive a row from *entity* by its primary key columns.
      Example
          select realtime.build_prepared_statement_sql('public.notes', '{"id"}'::text[], '{"bigint"}'::text[])
      */
          select
      'prepare ' || prepared_statement_name || ' as
          select
              exists(
                  select
                      1
                  from
                      ' || entity || '
                  where
                      ' || string_agg(quote_ident(pkc.name) || '=' || quote_nullable(pkc.value #>> '{}') , ' and ') || '
              )'
          from
              unnest(columns) pkc
          where
              pkc.is_pkey
          group by
              entity
      $$;


ALTER FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) OWNER TO supabase_admin;

--
-- Name: cast(text, regtype); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime."cast"(val text, type_ regtype) RETURNS jsonb
    LANGUAGE plpgsql IMMUTABLE
    AS $$
    declare
      res jsonb;
    begin
      execute format('select to_jsonb(%L::'|| type_::text || ')', val)  into res;
      return res;
    end
    $$;


ALTER FUNCTION realtime."cast"(val text, type_ regtype) OWNER TO supabase_admin;

--
-- Name: check_equality_op(realtime.equality_op, regtype, text, text); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) RETURNS boolean
    LANGUAGE plpgsql IMMUTABLE
    AS $$
      /*
      Casts *val_1* and *val_2* as type *type_* and check the *op* condition for truthiness
      */
      declare
          op_symbol text = (
              case
                  when op = 'eq' then '='
                  when op = 'neq' then '!='
                  when op = 'lt' then '<'
                  when op = 'lte' then '<='
                  when op = 'gt' then '>'
                  when op = 'gte' then '>='
                  when op = 'in' then '= any'
                  else 'UNKNOWN OP'
              end
          );
          res boolean;
      begin
          execute format(
              'select %L::'|| type_::text || ' ' || op_symbol
              || ' ( %L::'
              || (
                  case
                      when op = 'in' then type_::text || '[]'
                      else type_::text end
              )
              || ')', val_1, val_2) into res;
          return res;
      end;
      $$;


ALTER FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) OWNER TO supabase_admin;

--
-- Name: is_visible_through_filters(realtime.wal_column[], realtime.user_defined_filter[]); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) RETURNS boolean
    LANGUAGE sql IMMUTABLE
    AS $_$
    /*
    Should the record be visible (true) or filtered out (false) after *filters* are applied
    */
        select
            -- Default to allowed when no filters present
            $2 is null -- no filters. this should not happen because subscriptions has a default
            or array_length($2, 1) is null -- array length of an empty array is null
            or bool_and(
                coalesce(
                    realtime.check_equality_op(
                        op:=f.op,
                        type_:=coalesce(
                            col.type_oid::regtype, -- null when wal2json version <= 2.4
                            col.type_name::regtype
                        ),
                        -- cast jsonb to text
                        val_1:=col.value #>> '{}',
                        val_2:=f.value
                    ),
                    false -- if null, filter does not match
                )
            )
        from
            unnest(filters) f
            join unnest(columns) col
                on f.column_name = col.name;
    $_$;


ALTER FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) OWNER TO supabase_admin;

--
-- Name: list_changes(name, name, integer, integer); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) RETURNS SETOF realtime.wal_rls
    LANGUAGE sql
    SET log_min_messages TO 'fatal'
    AS $$
      with pub as (
        select
          concat_ws(
            ',',
            case when bool_or(pubinsert) then 'insert' else null end,
            case when bool_or(pubupdate) then 'update' else null end,
            case when bool_or(pubdelete) then 'delete' else null end
          ) as w2j_actions,
          coalesce(
            string_agg(
              realtime.quote_wal2json(format('%I.%I', schemaname, tablename)::regclass),
              ','
            ) filter (where ppt.tablename is not null and ppt.tablename not like '% %'),
            ''
          ) w2j_add_tables
        from
          pg_publication pp
          left join pg_publication_tables ppt
            on pp.pubname = ppt.pubname
        where
          pp.pubname = publication
        group by
          pp.pubname
        limit 1
      ),
      w2j as (
        select
          x.*, pub.w2j_add_tables
        from
          pub,
          pg_logical_slot_get_changes(
            slot_name, null, max_changes,
            'include-pk', 'true',
            'include-transaction', 'false',
            'include-timestamp', 'true',
            'include-type-oids', 'true',
            'format-version', '2',
            'actions', pub.w2j_actions,
            'add-tables', pub.w2j_add_tables
          ) x
      )
      select
        xyz.wal,
        xyz.is_rls_enabled,
        xyz.subscription_ids,
        xyz.errors
      from
        w2j,
        realtime.apply_rls(
          wal := w2j.data::jsonb,
          max_record_bytes := max_record_bytes
        ) xyz(wal, is_rls_enabled, subscription_ids, errors)
      where
        w2j.w2j_add_tables <> ''
        and xyz.subscription_ids[1] is not null
    $$;


ALTER FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) OWNER TO supabase_admin;

--
-- Name: quote_wal2json(regclass); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.quote_wal2json(entity regclass) RETURNS text
    LANGUAGE sql IMMUTABLE STRICT
    AS $$
      select
        (
          select string_agg('' || ch,'')
          from unnest(string_to_array(nsp.nspname::text, null)) with ordinality x(ch, idx)
          where
            not (x.idx = 1 and x.ch = '"')
            and not (
              x.idx = array_length(string_to_array(nsp.nspname::text, null), 1)
              and x.ch = '"'
            )
        )
        || '.'
        || (
          select string_agg('' || ch,'')
          from unnest(string_to_array(pc.relname::text, null)) with ordinality x(ch, idx)
          where
            not (x.idx = 1 and x.ch = '"')
            and not (
              x.idx = array_length(string_to_array(nsp.nspname::text, null), 1)
              and x.ch = '"'
            )
          )
      from
        pg_class pc
        join pg_namespace nsp
          on pc.relnamespace = nsp.oid
      where
        pc.oid = entity
    $$;


ALTER FUNCTION realtime.quote_wal2json(entity regclass) OWNER TO supabase_admin;

--
-- Name: send(jsonb, text, text, boolean); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.send(payload jsonb, event text, topic text, private boolean DEFAULT true) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
  BEGIN
    -- Set the topic configuration
    EXECUTE format('SET LOCAL realtime.topic TO %L', topic);

    -- Attempt to insert the message
    INSERT INTO realtime.messages (payload, event, topic, private, extension)
    VALUES (payload, event, topic, private, 'broadcast');
  EXCEPTION
    WHEN OTHERS THEN
      -- Capture and notify the error
      PERFORM pg_notify(
          'realtime:system',
          jsonb_build_object(
              'error', SQLERRM,
              'function', 'realtime.send',
              'event', event,
              'topic', topic,
              'private', private
          )::text
      );
  END;
END;
$$;


ALTER FUNCTION realtime.send(payload jsonb, event text, topic text, private boolean) OWNER TO supabase_admin;

--
-- Name: subscription_check_filters(); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.subscription_check_filters() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
    /*
    Validates that the user defined filters for a subscription:
    - refer to valid columns that the claimed role may access
    - values are coercable to the correct column type
    */
    declare
        col_names text[] = coalesce(
                array_agg(c.column_name order by c.ordinal_position),
                '{}'::text[]
            )
            from
                information_schema.columns c
            where
                format('%I.%I', c.table_schema, c.table_name)::regclass = new.entity
                and pg_catalog.has_column_privilege(
                    (new.claims ->> 'role'),
                    format('%I.%I', c.table_schema, c.table_name)::regclass,
                    c.column_name,
                    'SELECT'
                );
        filter realtime.user_defined_filter;
        col_type regtype;

        in_val jsonb;
    begin
        for filter in select * from unnest(new.filters) loop
            -- Filtered column is valid
            if not filter.column_name = any(col_names) then
                raise exception 'invalid column for filter %', filter.column_name;
            end if;

            -- Type is sanitized and safe for string interpolation
            col_type = (
                select atttypid::regtype
                from pg_catalog.pg_attribute
                where attrelid = new.entity
                      and attname = filter.column_name
            );
            if col_type is null then
                raise exception 'failed to lookup type for column %', filter.column_name;
            end if;

            -- Set maximum number of entries for in filter
            if filter.op = 'in'::realtime.equality_op then
                in_val = realtime.cast(filter.value, (col_type::text || '[]')::regtype);
                if coalesce(jsonb_array_length(in_val), 0) > 100 then
                    raise exception 'too many values for `in` filter. Maximum 100';
                end if;
            else
                -- raises an exception if value is not coercable to type
                perform realtime.cast(filter.value, col_type);
            end if;

        end loop;

        -- Apply consistent order to filters so the unique constraint on
        -- (subscription_id, entity, filters) can't be tricked by a different filter order
        new.filters = coalesce(
            array_agg(f order by f.column_name, f.op, f.value),
            '{}'
        ) from unnest(new.filters) f;

        return new;
    end;
    $$;


ALTER FUNCTION realtime.subscription_check_filters() OWNER TO supabase_admin;

--
-- Name: to_regrole(text); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.to_regrole(role_name text) RETURNS regrole
    LANGUAGE sql IMMUTABLE
    AS $$ select role_name::regrole $$;


ALTER FUNCTION realtime.to_regrole(role_name text) OWNER TO supabase_admin;

--
-- Name: topic(); Type: FUNCTION; Schema: realtime; Owner: supabase_realtime_admin
--

CREATE FUNCTION realtime.topic() RETURNS text
    LANGUAGE sql STABLE
    AS $$
select nullif(current_setting('realtime.topic', true), '')::text;
$$;


ALTER FUNCTION realtime.topic() OWNER TO supabase_realtime_admin;

--
-- Name: add_prefixes(text, text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.add_prefixes(_bucket_id text, _name text) RETURNS void
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
    prefixes text[];
BEGIN
    prefixes := "storage"."get_prefixes"("_name");

    IF array_length(prefixes, 1) > 0 THEN
        INSERT INTO storage.prefixes (name, bucket_id)
        SELECT UNNEST(prefixes) as name, "_bucket_id" ON CONFLICT DO NOTHING;
    END IF;
END;
$$;


ALTER FUNCTION storage.add_prefixes(_bucket_id text, _name text) OWNER TO supabase_storage_admin;

--
-- Name: can_insert_object(text, text, uuid, jsonb); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.can_insert_object(bucketid text, name text, owner uuid, metadata jsonb) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
  INSERT INTO "storage"."objects" ("bucket_id", "name", "owner", "metadata") VALUES (bucketid, name, owner, metadata);
  -- hack to rollback the successful insert
  RAISE sqlstate 'PT200' using
  message = 'ROLLBACK',
  detail = 'rollback successful insert';
END
$$;


ALTER FUNCTION storage.can_insert_object(bucketid text, name text, owner uuid, metadata jsonb) OWNER TO supabase_storage_admin;

--
-- Name: delete_prefix(text, text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.delete_prefix(_bucket_id text, _name text) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
    -- Check if we can delete the prefix
    IF EXISTS(
        SELECT FROM "storage"."prefixes"
        WHERE "prefixes"."bucket_id" = "_bucket_id"
          AND level = "storage"."get_level"("_name") + 1
          AND "prefixes"."name" COLLATE "C" LIKE "_name" || '/%'
        LIMIT 1
    )
    OR EXISTS(
        SELECT FROM "storage"."objects"
        WHERE "objects"."bucket_id" = "_bucket_id"
          AND "storage"."get_level"("objects"."name") = "storage"."get_level"("_name") + 1
          AND "objects"."name" COLLATE "C" LIKE "_name" || '/%'
        LIMIT 1
    ) THEN
    -- There are sub-objects, skip deletion
    RETURN false;
    ELSE
        DELETE FROM "storage"."prefixes"
        WHERE "prefixes"."bucket_id" = "_bucket_id"
          AND level = "storage"."get_level"("_name")
          AND "prefixes"."name" = "_name";
        RETURN true;
    END IF;
END;
$$;


ALTER FUNCTION storage.delete_prefix(_bucket_id text, _name text) OWNER TO supabase_storage_admin;

--
-- Name: delete_prefix_hierarchy_trigger(); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.delete_prefix_hierarchy_trigger() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    prefix text;
BEGIN
    prefix := "storage"."get_prefix"(OLD."name");

    IF coalesce(prefix, '') != '' THEN
        PERFORM "storage"."delete_prefix"(OLD."bucket_id", prefix);
    END IF;

    RETURN OLD;
END;
$$;


ALTER FUNCTION storage.delete_prefix_hierarchy_trigger() OWNER TO supabase_storage_admin;

--
-- Name: enforce_bucket_name_length(); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.enforce_bucket_name_length() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
begin
    if length(new.name) > 100 then
        raise exception 'bucket name "%" is too long (% characters). Max is 100.', new.name, length(new.name);
    end if;
    return new;
end;
$$;


ALTER FUNCTION storage.enforce_bucket_name_length() OWNER TO supabase_storage_admin;

--
-- Name: extension(text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.extension(name text) RETURNS text
    LANGUAGE plpgsql IMMUTABLE
    AS $$
DECLARE
    _parts text[];
    _filename text;
BEGIN
    SELECT string_to_array(name, '/') INTO _parts;
    SELECT _parts[array_length(_parts,1)] INTO _filename;
    RETURN reverse(split_part(reverse(_filename), '.', 1));
END
$$;


ALTER FUNCTION storage.extension(name text) OWNER TO supabase_storage_admin;

--
-- Name: filename(text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.filename(name text) RETURNS text
    LANGUAGE plpgsql
    AS $$
DECLARE
_parts text[];
BEGIN
	select string_to_array(name, '/') into _parts;
	return _parts[array_length(_parts,1)];
END
$$;


ALTER FUNCTION storage.filename(name text) OWNER TO supabase_storage_admin;

--
-- Name: foldername(text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.foldername(name text) RETURNS text[]
    LANGUAGE plpgsql IMMUTABLE
    AS $$
DECLARE
    _parts text[];
BEGIN
    -- Split on "/" to get path segments
    SELECT string_to_array(name, '/') INTO _parts;
    -- Return everything except the last segment
    RETURN _parts[1 : array_length(_parts,1) - 1];
END
$$;


ALTER FUNCTION storage.foldername(name text) OWNER TO supabase_storage_admin;

--
-- Name: get_level(text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.get_level(name text) RETURNS integer
    LANGUAGE sql IMMUTABLE STRICT
    AS $$
SELECT array_length(string_to_array("name", '/'), 1);
$$;


ALTER FUNCTION storage.get_level(name text) OWNER TO supabase_storage_admin;

--
-- Name: get_prefix(text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.get_prefix(name text) RETURNS text
    LANGUAGE sql IMMUTABLE STRICT
    AS $_$
SELECT
    CASE WHEN strpos("name", '/') > 0 THEN
             regexp_replace("name", '[\/]{1}[^\/]+\/?$', '')
         ELSE
             ''
        END;
$_$;


ALTER FUNCTION storage.get_prefix(name text) OWNER TO supabase_storage_admin;

--
-- Name: get_prefixes(text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.get_prefixes(name text) RETURNS text[]
    LANGUAGE plpgsql IMMUTABLE STRICT
    AS $$
DECLARE
    parts text[];
    prefixes text[];
    prefix text;
BEGIN
    -- Split the name into parts by '/'
    parts := string_to_array("name", '/');
    prefixes := '{}';

    -- Construct the prefixes, stopping one level below the last part
    FOR i IN 1..array_length(parts, 1) - 1 LOOP
            prefix := array_to_string(parts[1:i], '/');
            prefixes := array_append(prefixes, prefix);
    END LOOP;

    RETURN prefixes;
END;
$$;


ALTER FUNCTION storage.get_prefixes(name text) OWNER TO supabase_storage_admin;

--
-- Name: get_size_by_bucket(); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.get_size_by_bucket() RETURNS TABLE(size bigint, bucket_id text)
    LANGUAGE plpgsql STABLE
    AS $$
BEGIN
    return query
        select sum((metadata->>'size')::bigint) as size, obj.bucket_id
        from "storage".objects as obj
        group by obj.bucket_id;
END
$$;


ALTER FUNCTION storage.get_size_by_bucket() OWNER TO supabase_storage_admin;

--
-- Name: list_multipart_uploads_with_delimiter(text, text, text, integer, text, text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.list_multipart_uploads_with_delimiter(bucket_id text, prefix_param text, delimiter_param text, max_keys integer DEFAULT 100, next_key_token text DEFAULT ''::text, next_upload_token text DEFAULT ''::text) RETURNS TABLE(key text, id text, created_at timestamp with time zone)
    LANGUAGE plpgsql
    AS $_$
BEGIN
    RETURN QUERY EXECUTE
        'SELECT DISTINCT ON(key COLLATE "C") * from (
            SELECT
                CASE
                    WHEN position($2 IN substring(key from length($1) + 1)) > 0 THEN
                        substring(key from 1 for length($1) + position($2 IN substring(key from length($1) + 1)))
                    ELSE
                        key
                END AS key, id, created_at
            FROM
                storage.s3_multipart_uploads
            WHERE
                bucket_id = $5 AND
                key ILIKE $1 || ''%'' AND
                CASE
                    WHEN $4 != '''' AND $6 = '''' THEN
                        CASE
                            WHEN position($2 IN substring(key from length($1) + 1)) > 0 THEN
                                substring(key from 1 for length($1) + position($2 IN substring(key from length($1) + 1))) COLLATE "C" > $4
                            ELSE
                                key COLLATE "C" > $4
                            END
                    ELSE
                        true
                END AND
                CASE
                    WHEN $6 != '''' THEN
                        id COLLATE "C" > $6
                    ELSE
                        true
                    END
            ORDER BY
                key COLLATE "C" ASC, created_at ASC) as e order by key COLLATE "C" LIMIT $3'
        USING prefix_param, delimiter_param, max_keys, next_key_token, bucket_id, next_upload_token;
END;
$_$;


ALTER FUNCTION storage.list_multipart_uploads_with_delimiter(bucket_id text, prefix_param text, delimiter_param text, max_keys integer, next_key_token text, next_upload_token text) OWNER TO supabase_storage_admin;

--
-- Name: list_objects_with_delimiter(text, text, text, integer, text, text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.list_objects_with_delimiter(bucket_id text, prefix_param text, delimiter_param text, max_keys integer DEFAULT 100, start_after text DEFAULT ''::text, next_token text DEFAULT ''::text) RETURNS TABLE(name text, id uuid, metadata jsonb, updated_at timestamp with time zone)
    LANGUAGE plpgsql
    AS $_$
BEGIN
    RETURN QUERY EXECUTE
        'SELECT DISTINCT ON(name COLLATE "C") * from (
            SELECT
                CASE
                    WHEN position($2 IN substring(name from length($1) + 1)) > 0 THEN
                        substring(name from 1 for length($1) + position($2 IN substring(name from length($1) + 1)))
                    ELSE
                        name
                END AS name, id, metadata, updated_at
            FROM
                storage.objects
            WHERE
                bucket_id = $5 AND
                name ILIKE $1 || ''%'' AND
                CASE
                    WHEN $6 != '''' THEN
                    name COLLATE "C" > $6
                ELSE true END
                AND CASE
                    WHEN $4 != '''' THEN
                        CASE
                            WHEN position($2 IN substring(name from length($1) + 1)) > 0 THEN
                                substring(name from 1 for length($1) + position($2 IN substring(name from length($1) + 1))) COLLATE "C" > $4
                            ELSE
                                name COLLATE "C" > $4
                            END
                    ELSE
                        true
                END
            ORDER BY
                name COLLATE "C" ASC) as e order by name COLLATE "C" LIMIT $3'
        USING prefix_param, delimiter_param, max_keys, next_token, bucket_id, start_after;
END;
$_$;


ALTER FUNCTION storage.list_objects_with_delimiter(bucket_id text, prefix_param text, delimiter_param text, max_keys integer, start_after text, next_token text) OWNER TO supabase_storage_admin;

--
-- Name: objects_insert_prefix_trigger(); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.objects_insert_prefix_trigger() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    PERFORM "storage"."add_prefixes"(NEW."bucket_id", NEW."name");
    NEW.level := "storage"."get_level"(NEW."name");

    RETURN NEW;
END;
$$;


ALTER FUNCTION storage.objects_insert_prefix_trigger() OWNER TO supabase_storage_admin;

--
-- Name: objects_update_prefix_trigger(); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.objects_update_prefix_trigger() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    old_prefixes TEXT[];
BEGIN
    -- Ensure this is an update operation and the name has changed
    IF TG_OP = 'UPDATE' AND (NEW."name" <> OLD."name" OR NEW."bucket_id" <> OLD."bucket_id") THEN
        -- Retrieve old prefixes
        old_prefixes := "storage"."get_prefixes"(OLD."name");

        -- Remove old prefixes that are only used by this object
        WITH all_prefixes as (
            SELECT unnest(old_prefixes) as prefix
        ),
        can_delete_prefixes as (
             SELECT prefix
             FROM all_prefixes
             WHERE NOT EXISTS (
                 SELECT 1 FROM "storage"."objects"
                 WHERE "bucket_id" = OLD."bucket_id"
                   AND "name" <> OLD."name"
                   AND "name" LIKE (prefix || '%')
             )
         )
        DELETE FROM "storage"."prefixes" WHERE name IN (SELECT prefix FROM can_delete_prefixes);

        -- Add new prefixes
        PERFORM "storage"."add_prefixes"(NEW."bucket_id", NEW."name");
    END IF;
    -- Set the new level
    NEW."level" := "storage"."get_level"(NEW."name");

    RETURN NEW;
END;
$$;


ALTER FUNCTION storage.objects_update_prefix_trigger() OWNER TO supabase_storage_admin;

--
-- Name: operation(); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.operation() RETURNS text
    LANGUAGE plpgsql STABLE
    AS $$
BEGIN
    RETURN current_setting('storage.operation', true);
END;
$$;


ALTER FUNCTION storage.operation() OWNER TO supabase_storage_admin;

--
-- Name: prefixes_insert_trigger(); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.prefixes_insert_trigger() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    PERFORM "storage"."add_prefixes"(NEW."bucket_id", NEW."name");
    RETURN NEW;
END;
$$;


ALTER FUNCTION storage.prefixes_insert_trigger() OWNER TO supabase_storage_admin;

--
-- Name: search(text, text, integer, integer, integer, text, text, text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.search(prefix text, bucketname text, limits integer DEFAULT 100, levels integer DEFAULT 1, offsets integer DEFAULT 0, search text DEFAULT ''::text, sortcolumn text DEFAULT 'name'::text, sortorder text DEFAULT 'asc'::text) RETURNS TABLE(name text, id uuid, updated_at timestamp with time zone, created_at timestamp with time zone, last_accessed_at timestamp with time zone, metadata jsonb)
    LANGUAGE plpgsql
    AS $$
declare
    can_bypass_rls BOOLEAN;
begin
    SELECT rolbypassrls
    INTO can_bypass_rls
    FROM pg_roles
    WHERE rolname = coalesce(nullif(current_setting('role', true), 'none'), current_user);

    IF can_bypass_rls THEN
        RETURN QUERY SELECT * FROM storage.search_v1_optimised(prefix, bucketname, limits, levels, offsets, search, sortcolumn, sortorder);
    ELSE
        RETURN QUERY SELECT * FROM storage.search_legacy_v1(prefix, bucketname, limits, levels, offsets, search, sortcolumn, sortorder);
    END IF;
end;
$$;


ALTER FUNCTION storage.search(prefix text, bucketname text, limits integer, levels integer, offsets integer, search text, sortcolumn text, sortorder text) OWNER TO supabase_storage_admin;

--
-- Name: search_legacy_v1(text, text, integer, integer, integer, text, text, text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.search_legacy_v1(prefix text, bucketname text, limits integer DEFAULT 100, levels integer DEFAULT 1, offsets integer DEFAULT 0, search text DEFAULT ''::text, sortcolumn text DEFAULT 'name'::text, sortorder text DEFAULT 'asc'::text) RETURNS TABLE(name text, id uuid, updated_at timestamp with time zone, created_at timestamp with time zone, last_accessed_at timestamp with time zone, metadata jsonb)
    LANGUAGE plpgsql STABLE
    AS $_$
declare
    v_order_by text;
    v_sort_order text;
begin
    case
        when sortcolumn = 'name' then
            v_order_by = 'name';
        when sortcolumn = 'updated_at' then
            v_order_by = 'updated_at';
        when sortcolumn = 'created_at' then
            v_order_by = 'created_at';
        when sortcolumn = 'last_accessed_at' then
            v_order_by = 'last_accessed_at';
        else
            v_order_by = 'name';
        end case;

    case
        when sortorder = 'asc' then
            v_sort_order = 'asc';
        when sortorder = 'desc' then
            v_sort_order = 'desc';
        else
            v_sort_order = 'asc';
        end case;

    v_order_by = v_order_by || ' ' || v_sort_order;

    return query execute
        'with folders as (
           select path_tokens[$1] as folder
           from storage.objects
             where objects.name ilike $2 || $3 || ''%''
               and bucket_id = $4
               and array_length(objects.path_tokens, 1) <> $1
           group by folder
           order by folder ' || v_sort_order || '
     )
     (select folder as "name",
            null as id,
            null as updated_at,
            null as created_at,
            null as last_accessed_at,
            null as metadata from folders)
     union all
     (select path_tokens[$1] as "name",
            id,
            updated_at,
            created_at,
            last_accessed_at,
            metadata
     from storage.objects
     where objects.name ilike $2 || $3 || ''%''
       and bucket_id = $4
       and array_length(objects.path_tokens, 1) = $1
     order by ' || v_order_by || ')
     limit $5
     offset $6' using levels, prefix, search, bucketname, limits, offsets;
end;
$_$;


ALTER FUNCTION storage.search_legacy_v1(prefix text, bucketname text, limits integer, levels integer, offsets integer, search text, sortcolumn text, sortorder text) OWNER TO supabase_storage_admin;

--
-- Name: search_v1_optimised(text, text, integer, integer, integer, text, text, text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.search_v1_optimised(prefix text, bucketname text, limits integer DEFAULT 100, levels integer DEFAULT 1, offsets integer DEFAULT 0, search text DEFAULT ''::text, sortcolumn text DEFAULT 'name'::text, sortorder text DEFAULT 'asc'::text) RETURNS TABLE(name text, id uuid, updated_at timestamp with time zone, created_at timestamp with time zone, last_accessed_at timestamp with time zone, metadata jsonb)
    LANGUAGE plpgsql STABLE
    AS $_$
declare
    v_order_by text;
    v_sort_order text;
begin
    case
        when sortcolumn = 'name' then
            v_order_by = 'name';
        when sortcolumn = 'updated_at' then
            v_order_by = 'updated_at';
        when sortcolumn = 'created_at' then
            v_order_by = 'created_at';
        when sortcolumn = 'last_accessed_at' then
            v_order_by = 'last_accessed_at';
        else
            v_order_by = 'name';
        end case;

    case
        when sortorder = 'asc' then
            v_sort_order = 'asc';
        when sortorder = 'desc' then
            v_sort_order = 'desc';
        else
            v_sort_order = 'asc';
        end case;

    v_order_by = v_order_by || ' ' || v_sort_order;

    return query execute
        'with folders as (
           select (string_to_array(name, ''/''))[level] as name
           from storage.prefixes
             where lower(prefixes.name) like lower($2 || $3) || ''%''
               and bucket_id = $4
               and level = $1
           order by name ' || v_sort_order || '
     )
     (select name,
            null as id,
            null as updated_at,
            null as created_at,
            null as last_accessed_at,
            null as metadata from folders)
     union all
     (select path_tokens[level] as "name",
            id,
            updated_at,
            created_at,
            last_accessed_at,
            metadata
     from storage.objects
     where lower(objects.name) like lower($2 || $3) || ''%''
       and bucket_id = $4
       and level = $1
     order by ' || v_order_by || ')
     limit $5
     offset $6' using levels, prefix, search, bucketname, limits, offsets;
end;
$_$;


ALTER FUNCTION storage.search_v1_optimised(prefix text, bucketname text, limits integer, levels integer, offsets integer, search text, sortcolumn text, sortorder text) OWNER TO supabase_storage_admin;

--
-- Name: search_v2(text, text, integer, integer, text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.search_v2(prefix text, bucket_name text, limits integer DEFAULT 100, levels integer DEFAULT 1, start_after text DEFAULT ''::text) RETURNS TABLE(key text, name text, id uuid, updated_at timestamp with time zone, created_at timestamp with time zone, metadata jsonb)
    LANGUAGE plpgsql STABLE
    AS $_$
BEGIN
    RETURN query EXECUTE
        $sql$
        SELECT * FROM (
            (
                SELECT
                    split_part(name, '/', $4) AS key,
                    name || '/' AS name,
                    NULL::uuid AS id,
                    NULL::timestamptz AS updated_at,
                    NULL::timestamptz AS created_at,
                    NULL::jsonb AS metadata
                FROM storage.prefixes
                WHERE name COLLATE "C" LIKE $1 || '%'
                AND bucket_id = $2
                AND level = $4
                AND name COLLATE "C" > $5
                ORDER BY prefixes.name COLLATE "C" LIMIT $3
            )
            UNION ALL
            (SELECT split_part(name, '/', $4) AS key,
                name,
                id,
                updated_at,
                created_at,
                metadata
            FROM storage.objects
            WHERE name COLLATE "C" LIKE $1 || '%'
                AND bucket_id = $2
                AND level = $4
                AND name COLLATE "C" > $5
            ORDER BY name COLLATE "C" LIMIT $3)
        ) obj
        ORDER BY name COLLATE "C" LIMIT $3;
        $sql$
        USING prefix, bucket_name, limits, levels, start_after;
END;
$_$;


ALTER FUNCTION storage.search_v2(prefix text, bucket_name text, limits integer, levels integer, start_after text) OWNER TO supabase_storage_admin;

--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW; 
END;
$$;


ALTER FUNCTION storage.update_updated_at_column() OWNER TO supabase_storage_admin;

--
-- Name: http_request(); Type: FUNCTION; Schema: supabase_functions; Owner: supabase_functions_admin
--

CREATE FUNCTION supabase_functions.http_request() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    SET search_path TO 'supabase_functions'
    AS $$
    DECLARE
      request_id bigint;
      payload jsonb;
      url text := TG_ARGV[0]::text;
      method text := TG_ARGV[1]::text;
      headers jsonb DEFAULT '{}'::jsonb;
      params jsonb DEFAULT '{}'::jsonb;
      timeout_ms integer DEFAULT 1000;
    BEGIN
      IF url IS NULL OR url = 'null' THEN
        RAISE EXCEPTION 'url argument is missing';
      END IF;

      IF method IS NULL OR method = 'null' THEN
        RAISE EXCEPTION 'method argument is missing';
      END IF;

      IF TG_ARGV[2] IS NULL OR TG_ARGV[2] = 'null' THEN
        headers = '{"Content-Type": "application/json"}'::jsonb;
      ELSE
        headers = TG_ARGV[2]::jsonb;
      END IF;

      IF TG_ARGV[3] IS NULL OR TG_ARGV[3] = 'null' THEN
        params = '{}'::jsonb;
      ELSE
        params = TG_ARGV[3]::jsonb;
      END IF;

      IF TG_ARGV[4] IS NULL OR TG_ARGV[4] = 'null' THEN
        timeout_ms = 1000;
      ELSE
        timeout_ms = TG_ARGV[4]::integer;
      END IF;

      CASE
        WHEN method = 'GET' THEN
          SELECT http_get INTO request_id FROM net.http_get(
            url,
            params,
            headers,
            timeout_ms
          );
        WHEN method = 'POST' THEN
          payload = jsonb_build_object(
            'old_record', OLD,
            'record', NEW,
            'type', TG_OP,
            'table', TG_TABLE_NAME,
            'schema', TG_TABLE_SCHEMA
          );

          SELECT http_post INTO request_id FROM net.http_post(
            url,
            payload,
            params,
            headers,
            timeout_ms
          );
        ELSE
          RAISE EXCEPTION 'method argument % is invalid', method;
      END CASE;

      INSERT INTO supabase_functions.hooks
        (hook_table_id, hook_name, request_id)
      VALUES
        (TG_RELID, TG_NAME, request_id);

      RETURN NEW;
    END
  $$;


ALTER FUNCTION supabase_functions.http_request() OWNER TO supabase_functions_admin;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: extensions; Type: TABLE; Schema: _realtime; Owner: supabase_admin
--

CREATE TABLE _realtime.extensions (
    id uuid NOT NULL,
    type text,
    settings jsonb,
    tenant_external_id text,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


ALTER TABLE _realtime.extensions OWNER TO supabase_admin;

--
-- Name: schema_migrations; Type: TABLE; Schema: _realtime; Owner: supabase_admin
--

CREATE TABLE _realtime.schema_migrations (
    version bigint NOT NULL,
    inserted_at timestamp(0) without time zone
);


ALTER TABLE _realtime.schema_migrations OWNER TO supabase_admin;

--
-- Name: tenants; Type: TABLE; Schema: _realtime; Owner: supabase_admin
--

CREATE TABLE _realtime.tenants (
    id uuid NOT NULL,
    name text,
    external_id text,
    jwt_secret text,
    max_concurrent_users integer DEFAULT 200 NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    max_events_per_second integer DEFAULT 100 NOT NULL,
    postgres_cdc_default text DEFAULT 'postgres_cdc_rls'::text,
    max_bytes_per_second integer DEFAULT 100000 NOT NULL,
    max_channels_per_client integer DEFAULT 100 NOT NULL,
    max_joins_per_second integer DEFAULT 500 NOT NULL,
    suspend boolean DEFAULT false,
    jwt_jwks jsonb,
    notify_private_alpha boolean DEFAULT false,
    private_only boolean DEFAULT false NOT NULL
);


ALTER TABLE _realtime.tenants OWNER TO supabase_admin;

--
-- Name: audit_log_entries; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.audit_log_entries (
    instance_id uuid,
    id uuid NOT NULL,
    payload json,
    created_at timestamp with time zone,
    ip_address character varying(64) DEFAULT ''::character varying NOT NULL
);


ALTER TABLE auth.audit_log_entries OWNER TO supabase_auth_admin;

--
-- Name: TABLE audit_log_entries; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.audit_log_entries IS 'Auth: Audit trail for user actions.';


--
-- Name: flow_state; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.flow_state (
    id uuid NOT NULL,
    user_id uuid,
    auth_code text NOT NULL,
    code_challenge_method auth.code_challenge_method NOT NULL,
    code_challenge text NOT NULL,
    provider_type text NOT NULL,
    provider_access_token text,
    provider_refresh_token text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    authentication_method text NOT NULL,
    auth_code_issued_at timestamp with time zone
);


ALTER TABLE auth.flow_state OWNER TO supabase_auth_admin;

--
-- Name: TABLE flow_state; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.flow_state IS 'stores metadata for pkce logins';


--
-- Name: identities; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.identities (
    provider_id text NOT NULL,
    user_id uuid NOT NULL,
    identity_data jsonb NOT NULL,
    provider text NOT NULL,
    last_sign_in_at timestamp with time zone,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    email text GENERATED ALWAYS AS (lower((identity_data ->> 'email'::text))) STORED,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


ALTER TABLE auth.identities OWNER TO supabase_auth_admin;

--
-- Name: TABLE identities; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.identities IS 'Auth: Stores identities associated to a user.';


--
-- Name: COLUMN identities.email; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON COLUMN auth.identities.email IS 'Auth: Email is a generated column that references the optional email property in the identity_data';


--
-- Name: instances; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.instances (
    id uuid NOT NULL,
    uuid uuid,
    raw_base_config text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone
);


ALTER TABLE auth.instances OWNER TO supabase_auth_admin;

--
-- Name: TABLE instances; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.instances IS 'Auth: Manages users across multiple sites.';


--
-- Name: mfa_amr_claims; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.mfa_amr_claims (
    session_id uuid NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    authentication_method text NOT NULL,
    id uuid NOT NULL
);


ALTER TABLE auth.mfa_amr_claims OWNER TO supabase_auth_admin;

--
-- Name: TABLE mfa_amr_claims; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.mfa_amr_claims IS 'auth: stores authenticator method reference claims for multi factor authentication';


--
-- Name: mfa_challenges; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.mfa_challenges (
    id uuid NOT NULL,
    factor_id uuid NOT NULL,
    created_at timestamp with time zone NOT NULL,
    verified_at timestamp with time zone,
    ip_address inet NOT NULL,
    otp_code text,
    web_authn_session_data jsonb
);


ALTER TABLE auth.mfa_challenges OWNER TO supabase_auth_admin;

--
-- Name: TABLE mfa_challenges; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.mfa_challenges IS 'auth: stores metadata about challenge requests made';


--
-- Name: mfa_factors; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.mfa_factors (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    friendly_name text,
    factor_type auth.factor_type NOT NULL,
    status auth.factor_status NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    secret text,
    phone text,
    last_challenged_at timestamp with time zone,
    web_authn_credential jsonb,
    web_authn_aaguid uuid
);


ALTER TABLE auth.mfa_factors OWNER TO supabase_auth_admin;

--
-- Name: TABLE mfa_factors; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.mfa_factors IS 'auth: stores metadata about factors';


--
-- Name: one_time_tokens; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.one_time_tokens (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    token_type auth.one_time_token_type NOT NULL,
    token_hash text NOT NULL,
    relates_to text NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    CONSTRAINT one_time_tokens_token_hash_check CHECK ((char_length(token_hash) > 0))
);


ALTER TABLE auth.one_time_tokens OWNER TO supabase_auth_admin;

--
-- Name: refresh_tokens; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.refresh_tokens (
    instance_id uuid,
    id bigint NOT NULL,
    token character varying(255),
    user_id character varying(255),
    revoked boolean,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    parent character varying(255),
    session_id uuid
);


ALTER TABLE auth.refresh_tokens OWNER TO supabase_auth_admin;

--
-- Name: TABLE refresh_tokens; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.refresh_tokens IS 'Auth: Store of tokens used to refresh JWT tokens once they expire.';


--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE; Schema: auth; Owner: supabase_auth_admin
--

CREATE SEQUENCE auth.refresh_tokens_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE auth.refresh_tokens_id_seq OWNER TO supabase_auth_admin;

--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE OWNED BY; Schema: auth; Owner: supabase_auth_admin
--

ALTER SEQUENCE auth.refresh_tokens_id_seq OWNED BY auth.refresh_tokens.id;


--
-- Name: saml_providers; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.saml_providers (
    id uuid NOT NULL,
    sso_provider_id uuid NOT NULL,
    entity_id text NOT NULL,
    metadata_xml text NOT NULL,
    metadata_url text,
    attribute_mapping jsonb,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    name_id_format text,
    CONSTRAINT "entity_id not empty" CHECK ((char_length(entity_id) > 0)),
    CONSTRAINT "metadata_url not empty" CHECK (((metadata_url = NULL::text) OR (char_length(metadata_url) > 0))),
    CONSTRAINT "metadata_xml not empty" CHECK ((char_length(metadata_xml) > 0))
);


ALTER TABLE auth.saml_providers OWNER TO supabase_auth_admin;

--
-- Name: TABLE saml_providers; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.saml_providers IS 'Auth: Manages SAML Identity Provider connections.';


--
-- Name: saml_relay_states; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.saml_relay_states (
    id uuid NOT NULL,
    sso_provider_id uuid NOT NULL,
    request_id text NOT NULL,
    for_email text,
    redirect_to text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    flow_state_id uuid,
    CONSTRAINT "request_id not empty" CHECK ((char_length(request_id) > 0))
);


ALTER TABLE auth.saml_relay_states OWNER TO supabase_auth_admin;

--
-- Name: TABLE saml_relay_states; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.saml_relay_states IS 'Auth: Contains SAML Relay State information for each Service Provider initiated login.';


--
-- Name: schema_migrations; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.schema_migrations (
    version character varying(255) NOT NULL
);


ALTER TABLE auth.schema_migrations OWNER TO supabase_auth_admin;

--
-- Name: TABLE schema_migrations; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.schema_migrations IS 'Auth: Manages updates to the auth system.';


--
-- Name: sessions; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.sessions (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    factor_id uuid,
    aal auth.aal_level,
    not_after timestamp with time zone,
    refreshed_at timestamp without time zone,
    user_agent text,
    ip inet,
    tag text
);


ALTER TABLE auth.sessions OWNER TO supabase_auth_admin;

--
-- Name: TABLE sessions; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.sessions IS 'Auth: Stores session data associated to a user.';


--
-- Name: COLUMN sessions.not_after; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON COLUMN auth.sessions.not_after IS 'Auth: Not after is a nullable column that contains a timestamp after which the session should be regarded as expired.';


--
-- Name: sso_domains; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.sso_domains (
    id uuid NOT NULL,
    sso_provider_id uuid NOT NULL,
    domain text NOT NULL,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    CONSTRAINT "domain not empty" CHECK ((char_length(domain) > 0))
);


ALTER TABLE auth.sso_domains OWNER TO supabase_auth_admin;

--
-- Name: TABLE sso_domains; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.sso_domains IS 'Auth: Manages SSO email address domain mapping to an SSO Identity Provider.';


--
-- Name: sso_providers; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.sso_providers (
    id uuid NOT NULL,
    resource_id text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    CONSTRAINT "resource_id not empty" CHECK (((resource_id = NULL::text) OR (char_length(resource_id) > 0)))
);


ALTER TABLE auth.sso_providers OWNER TO supabase_auth_admin;

--
-- Name: TABLE sso_providers; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.sso_providers IS 'Auth: Manages SSO identity provider information; see saml_providers for SAML.';


--
-- Name: COLUMN sso_providers.resource_id; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON COLUMN auth.sso_providers.resource_id IS 'Auth: Uniquely identifies a SSO provider according to a user-chosen resource ID (case insensitive), useful in infrastructure as code.';


--
-- Name: users; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.users (
    instance_id uuid,
    id uuid NOT NULL,
    aud character varying(255),
    role character varying(255),
    email character varying(255),
    encrypted_password character varying(255),
    email_confirmed_at timestamp with time zone,
    invited_at timestamp with time zone,
    confirmation_token character varying(255),
    confirmation_sent_at timestamp with time zone,
    recovery_token character varying(255),
    recovery_sent_at timestamp with time zone,
    email_change_token_new character varying(255),
    email_change character varying(255),
    email_change_sent_at timestamp with time zone,
    last_sign_in_at timestamp with time zone,
    raw_app_meta_data jsonb,
    raw_user_meta_data jsonb,
    is_super_admin boolean,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    phone text DEFAULT NULL::character varying,
    phone_confirmed_at timestamp with time zone,
    phone_change text DEFAULT ''::character varying,
    phone_change_token character varying(255) DEFAULT ''::character varying,
    phone_change_sent_at timestamp with time zone,
    confirmed_at timestamp with time zone GENERATED ALWAYS AS (LEAST(email_confirmed_at, phone_confirmed_at)) STORED,
    email_change_token_current character varying(255) DEFAULT ''::character varying,
    email_change_confirm_status smallint DEFAULT 0,
    banned_until timestamp with time zone,
    reauthentication_token character varying(255) DEFAULT ''::character varying,
    reauthentication_sent_at timestamp with time zone,
    is_sso_user boolean DEFAULT false NOT NULL,
    deleted_at timestamp with time zone,
    is_anonymous boolean DEFAULT false NOT NULL,
    CONSTRAINT users_email_change_confirm_status_check CHECK (((email_change_confirm_status >= 0) AND (email_change_confirm_status <= 2)))
);


ALTER TABLE auth.users OWNER TO supabase_auth_admin;

--
-- Name: TABLE users; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.users IS 'Auth: Stores user login data within a secure schema.';


--
-- Name: COLUMN users.is_sso_user; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON COLUMN auth.users.is_sso_user IS 'Auth: Set this column to true when the account comes from SSO. These accounts can have duplicate emails.';


--
-- Name: membership-look; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."membership-look" (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    email character varying NOT NULL,
    membership_level character varying DEFAULT '免费'::character varying NOT NULL,
    word_count_limit integer,
    word_count_used integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    user_id uuid,
    is_verified boolean DEFAULT false,
    daily_free_quota integer DEFAULT 0,
    daily_free_used integer DEFAULT 0,
    last_free_reset_date date DEFAULT CURRENT_DATE,
    reward_quota integer DEFAULT 0,
    reward_used integer DEFAULT 0
);


ALTER TABLE public."membership-look" OWNER TO postgres;

--
-- Name: COLUMN "membership-look".reward_quota; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public."membership-look".reward_quota IS '奖励额度总量（从true表同步）';


--
-- Name: COLUMN "membership-look".reward_used; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public."membership-look".reward_used IS '奖励额度已使用量（从true表同步）';


--
-- Name: membership-true; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."membership-true" (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    email character varying NOT NULL,
    membership_level character varying DEFAULT '免费'::character varying NOT NULL,
    word_count_limit integer,
    word_count_used integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    user_id uuid,
    is_verified boolean DEFAULT false,
    daily_free_quota integer DEFAULT 0,
    daily_free_used integer DEFAULT 0,
    last_free_reset_date date DEFAULT CURRENT_DATE,
    reward_quota integer DEFAULT 0,
    reward_used integer DEFAULT 0
);


ALTER TABLE public."membership-true" OWNER TO postgres;

--
-- Name: COLUMN "membership-true".reward_quota; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public."membership-true".reward_quota IS '奖励额度总量';


--
-- Name: COLUMN "membership-true".reward_used; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public."membership-true".reward_used IS '奖励额度已使用量';


--
-- Name: novel_files; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.novel_files (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid,
    file_name text NOT NULL,
    file_path text NOT NULL,
    file_size bigint,
    mime_type text DEFAULT 'text/plain'::text,
    upload_time timestamp with time zone DEFAULT now(),
    minio_bucket text DEFAULT 'xiaoshuo'::text,
    minio_object_key text NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    work_title text
);


ALTER TABLE public.novel_files OWNER TO postgres;

--
-- Name: propmt-neirong; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."propmt-neirong" (
    prompt_id uuid NOT NULL,
    content text NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    title text DEFAULT ''::text NOT NULL,
    author_display_id character varying(100),
    usage_count integer DEFAULT 0 NOT NULL
);


ALTER TABLE public."propmt-neirong" OWNER TO postgres;

--
-- Name: COLUMN "propmt-neirong".title; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public."propmt-neirong".title IS '提示词标题名称';


--
-- Name: propmt-zhanshi; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."propmt-zhanshi" (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    title character varying(255) NOT NULL,
    description text,
    category character varying(100) DEFAULT 'official'::character varying NOT NULL,
    type character varying(50) NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    created_by character varying(255),
    author_display_id character varying(100),
    usage_count integer DEFAULT 0 NOT NULL
);


ALTER TABLE public."propmt-zhanshi" OWNER TO postgres;

--
-- Name: messages; Type: TABLE; Schema: realtime; Owner: supabase_realtime_admin
--

CREATE TABLE realtime.messages (
    topic text NOT NULL,
    extension text NOT NULL,
    payload jsonb,
    event text,
    private boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    id uuid DEFAULT gen_random_uuid() NOT NULL
)
PARTITION BY RANGE (inserted_at);


ALTER TABLE realtime.messages OWNER TO supabase_realtime_admin;

--
-- Name: schema_migrations; Type: TABLE; Schema: realtime; Owner: supabase_admin
--

CREATE TABLE realtime.schema_migrations (
    version bigint NOT NULL,
    inserted_at timestamp(0) without time zone
);


ALTER TABLE realtime.schema_migrations OWNER TO supabase_admin;

--
-- Name: subscription; Type: TABLE; Schema: realtime; Owner: supabase_admin
--

CREATE TABLE realtime.subscription (
    id bigint NOT NULL,
    subscription_id uuid NOT NULL,
    entity regclass NOT NULL,
    filters realtime.user_defined_filter[] DEFAULT '{}'::realtime.user_defined_filter[] NOT NULL,
    claims jsonb NOT NULL,
    claims_role regrole GENERATED ALWAYS AS (realtime.to_regrole((claims ->> 'role'::text))) STORED NOT NULL,
    created_at timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);


ALTER TABLE realtime.subscription OWNER TO supabase_admin;

--
-- Name: subscription_id_seq; Type: SEQUENCE; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE realtime.subscription ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME realtime.subscription_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: buckets; Type: TABLE; Schema: storage; Owner: supabase_storage_admin
--

CREATE TABLE storage.buckets (
    id text NOT NULL,
    name text NOT NULL,
    owner uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    public boolean DEFAULT false,
    avif_autodetection boolean DEFAULT false,
    file_size_limit bigint,
    allowed_mime_types text[],
    owner_id text,
    type storage.buckettype DEFAULT 'STANDARD'::storage.buckettype NOT NULL
);


ALTER TABLE storage.buckets OWNER TO supabase_storage_admin;

--
-- Name: COLUMN buckets.owner; Type: COMMENT; Schema: storage; Owner: supabase_storage_admin
--

COMMENT ON COLUMN storage.buckets.owner IS 'Field is deprecated, use owner_id instead';


--
-- Name: buckets_analytics; Type: TABLE; Schema: storage; Owner: supabase_storage_admin
--

CREATE TABLE storage.buckets_analytics (
    id text NOT NULL,
    type storage.buckettype DEFAULT 'ANALYTICS'::storage.buckettype NOT NULL,
    format text DEFAULT 'ICEBERG'::text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE storage.buckets_analytics OWNER TO supabase_storage_admin;

--
-- Name: iceberg_namespaces; Type: TABLE; Schema: storage; Owner: supabase_storage_admin
--

CREATE TABLE storage.iceberg_namespaces (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    bucket_id text NOT NULL,
    name text NOT NULL COLLATE pg_catalog."C",
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE storage.iceberg_namespaces OWNER TO supabase_storage_admin;

--
-- Name: iceberg_tables; Type: TABLE; Schema: storage; Owner: supabase_storage_admin
--

CREATE TABLE storage.iceberg_tables (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    namespace_id uuid NOT NULL,
    bucket_id text NOT NULL,
    name text NOT NULL COLLATE pg_catalog."C",
    location text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE storage.iceberg_tables OWNER TO supabase_storage_admin;

--
-- Name: migrations; Type: TABLE; Schema: storage; Owner: supabase_storage_admin
--

CREATE TABLE storage.migrations (
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    hash character varying(40) NOT NULL,
    executed_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE storage.migrations OWNER TO supabase_storage_admin;

--
-- Name: objects; Type: TABLE; Schema: storage; Owner: supabase_storage_admin
--

CREATE TABLE storage.objects (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    bucket_id text,
    name text,
    owner uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    last_accessed_at timestamp with time zone DEFAULT now(),
    metadata jsonb,
    path_tokens text[] GENERATED ALWAYS AS (string_to_array(name, '/'::text)) STORED,
    version text,
    owner_id text,
    user_metadata jsonb,
    level integer
);


ALTER TABLE storage.objects OWNER TO supabase_storage_admin;

--
-- Name: COLUMN objects.owner; Type: COMMENT; Schema: storage; Owner: supabase_storage_admin
--

COMMENT ON COLUMN storage.objects.owner IS 'Field is deprecated, use owner_id instead';


--
-- Name: prefixes; Type: TABLE; Schema: storage; Owner: supabase_storage_admin
--

CREATE TABLE storage.prefixes (
    bucket_id text NOT NULL,
    name text NOT NULL COLLATE pg_catalog."C",
    level integer GENERATED ALWAYS AS (storage.get_level(name)) STORED NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


ALTER TABLE storage.prefixes OWNER TO supabase_storage_admin;

--
-- Name: s3_multipart_uploads; Type: TABLE; Schema: storage; Owner: supabase_storage_admin
--

CREATE TABLE storage.s3_multipart_uploads (
    id text NOT NULL,
    in_progress_size bigint DEFAULT 0 NOT NULL,
    upload_signature text NOT NULL,
    bucket_id text NOT NULL,
    key text NOT NULL COLLATE pg_catalog."C",
    version text NOT NULL,
    owner_id text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    user_metadata jsonb
);


ALTER TABLE storage.s3_multipart_uploads OWNER TO supabase_storage_admin;

--
-- Name: s3_multipart_uploads_parts; Type: TABLE; Schema: storage; Owner: supabase_storage_admin
--

CREATE TABLE storage.s3_multipart_uploads_parts (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    upload_id text NOT NULL,
    size bigint DEFAULT 0 NOT NULL,
    part_number integer NOT NULL,
    bucket_id text NOT NULL,
    key text NOT NULL COLLATE pg_catalog."C",
    etag text NOT NULL,
    owner_id text,
    version text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE storage.s3_multipart_uploads_parts OWNER TO supabase_storage_admin;

--
-- Name: hooks; Type: TABLE; Schema: supabase_functions; Owner: supabase_functions_admin
--

CREATE TABLE supabase_functions.hooks (
    id bigint NOT NULL,
    hook_table_id integer NOT NULL,
    hook_name text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    request_id bigint
);


ALTER TABLE supabase_functions.hooks OWNER TO supabase_functions_admin;

--
-- Name: TABLE hooks; Type: COMMENT; Schema: supabase_functions; Owner: supabase_functions_admin
--

COMMENT ON TABLE supabase_functions.hooks IS 'Supabase Functions Hooks: Audit trail for triggered hooks.';


--
-- Name: hooks_id_seq; Type: SEQUENCE; Schema: supabase_functions; Owner: supabase_functions_admin
--

CREATE SEQUENCE supabase_functions.hooks_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE supabase_functions.hooks_id_seq OWNER TO supabase_functions_admin;

--
-- Name: hooks_id_seq; Type: SEQUENCE OWNED BY; Schema: supabase_functions; Owner: supabase_functions_admin
--

ALTER SEQUENCE supabase_functions.hooks_id_seq OWNED BY supabase_functions.hooks.id;


--
-- Name: migrations; Type: TABLE; Schema: supabase_functions; Owner: supabase_functions_admin
--

CREATE TABLE supabase_functions.migrations (
    version text NOT NULL,
    inserted_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE supabase_functions.migrations OWNER TO supabase_functions_admin;

--
-- Name: refresh_tokens id; Type: DEFAULT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.refresh_tokens ALTER COLUMN id SET DEFAULT nextval('auth.refresh_tokens_id_seq'::regclass);


--
-- Name: hooks id; Type: DEFAULT; Schema: supabase_functions; Owner: supabase_functions_admin
--

ALTER TABLE ONLY supabase_functions.hooks ALTER COLUMN id SET DEFAULT nextval('supabase_functions.hooks_id_seq'::regclass);


--
-- Data for Name: extensions; Type: TABLE DATA; Schema: _realtime; Owner: supabase_admin
--

COPY _realtime.extensions (id, type, settings, tenant_external_id, inserted_at, updated_at) FROM stdin;
0775c670-f50b-49f1-aab8-b23746701337	postgres_cdc_rls	{"region": "us-east-1", "db_host": "QhixI0o7PYIABziLUL4f0A==", "db_name": "sWBpZNdjggEPTQVlI52Zfw==", "db_port": "+enMDFi1J/3IrrquHHwUmA==", "db_user": "uxbEq/zz8DXVD53TOI1zmw==", "slot_name": "supabase_realtime_replication_slot", "db_password": "4nXDsx9OLqOJFwwLgYkF1E82uuGZrojyd3by3+a2jj8=", "publication": "supabase_realtime", "ssl_enforced": false, "poll_interval_ms": 100, "poll_max_changes": 100, "poll_max_record_bytes": 1048576}	realtime-dev	2025-08-03 05:54:55	2025-08-03 05:54:55
\.


--
-- Data for Name: schema_migrations; Type: TABLE DATA; Schema: _realtime; Owner: supabase_admin
--

COPY _realtime.schema_migrations (version, inserted_at) FROM stdin;
20210706140551	2025-08-03 05:40:44
20220329161857	2025-08-03 05:40:45
20220410212326	2025-08-03 05:40:45
20220506102948	2025-08-03 05:40:45
20220527210857	2025-08-03 05:40:45
20220815211129	2025-08-03 05:40:45
20220815215024	2025-08-03 05:40:45
20220818141501	2025-08-03 05:40:45
20221018173709	2025-08-03 05:40:45
20221102172703	2025-08-03 05:40:45
20221223010058	2025-08-03 05:40:45
20230110180046	2025-08-03 05:40:45
20230810220907	2025-08-03 05:40:45
20230810220924	2025-08-03 05:40:45
20231024094642	2025-08-03 05:40:45
20240306114423	2025-08-03 05:40:45
20240418082835	2025-08-03 05:40:45
20240625211759	2025-08-03 05:40:45
20240704172020	2025-08-03 05:40:45
20240902173232	2025-08-03 05:40:45
20241106103258	2025-08-03 05:40:45
\.


--
-- Data for Name: tenants; Type: TABLE DATA; Schema: _realtime; Owner: supabase_admin
--

COPY _realtime.tenants (id, name, external_id, jwt_secret, max_concurrent_users, inserted_at, updated_at, max_events_per_second, postgres_cdc_default, max_bytes_per_second, max_channels_per_client, max_joins_per_second, suspend, jwt_jwks, notify_private_alpha, private_only) FROM stdin;
d59e9000-490a-4e5c-bb17-371df7426256	realtime-dev	realtime-dev	E5F8LBU3wKksAM30cEkxapvgr554NbBkz2mAGmK0d/K9as3HKsV/mEgRknprWVgbn/UBK23mf7uVJjXn3etQCU82uuGZrojyd3by3+a2jj8=	200	2025-08-03 05:54:55	2025-08-03 05:54:55	100	postgres_cdc_rls	100000	100	100	f	\N	f	f
\.


--
-- Data for Name: audit_log_entries; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.audit_log_entries (instance_id, id, payload, created_at, ip_address) FROM stdin;
********-0000-0000-0000-************	342bcfcd-0a57-4094-9747-0c56e0160665	{"action":"user_signedup","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"000643ba-c3bb-4e09-b5ce-dc184e3a1880","user_phone":""}}	2025-06-10 10:01:29.625077+00	
********-0000-0000-0000-************	7e6ffdad-7078-4710-98b9-c954d70d4344	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"000643ba-c3bb-4e09-b5ce-dc184e3a1880","user_phone":""}}	2025-06-10 10:01:33.056559+00	
********-0000-0000-0000-************	4e302c74-b85a-476f-85b1-22a10b128779	{"action":"user_signedup","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"c8459552-3d20-4fdd-9b1f-b1023b5fc103","user_phone":""}}	2025-06-10 10:01:41.959846+00	
********-0000-0000-0000-************	c2080fb6-8c26-4545-b1cd-dc61f475cc0f	{"action":"login","actor_id":"c8459552-3d20-4fdd-9b1f-b1023b5fc103","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-10 10:14:03.124137+00	
********-0000-0000-0000-************	b8742cb4-773f-487b-9e5e-b63f5c5436c5	{"action":"logout","actor_id":"c8459552-3d20-4fdd-9b1f-b1023b5fc103","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-06-10 10:14:09.848898+00	
********-0000-0000-0000-************	6880b81e-55e0-4c81-a3ea-a3792035c4ab	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"c8459552-3d20-4fdd-9b1f-b1023b5fc103","user_phone":""}}	2025-06-10 10:14:36.399431+00	
********-0000-0000-0000-************	94ead822-89bc-4126-8bed-b36992b723b8	{"action":"user_confirmation_requested","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-06-10 10:14:47.419956+00	
********-0000-0000-0000-************	4e8b00da-e1d3-47e8-8815-cdc7751b17c8	{"action":"user_signedup","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"team"}	2025-06-10 10:15:02.073023+00	
********-0000-0000-0000-************	ff2af899-1df9-493b-8a09-b3d86b1dc2d6	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-10 11:14:32.998085+00	
********-0000-0000-0000-************	01ae40d5-eeb8-40e8-a363-557478bfc435	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-10 11:14:33.000857+00	
********-0000-0000-0000-************	d7fb7168-a5a2-4de5-aaa6-8ad2fad122c5	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-10 13:41:18.156036+00	
********-0000-0000-0000-************	2f37d409-7b2e-495e-a6fa-7645c8966a36	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-10 13:41:18.158694+00	
********-0000-0000-0000-************	c83f11cb-4d1c-4d73-8570-984f321a490a	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-10 14:52:04.817325+00	
********-0000-0000-0000-************	30068a03-e6e7-4ee8-828a-589314640879	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-10 14:52:04.819745+00	
********-0000-0000-0000-************	674cc3c9-e255-41aa-b240-fe479643c28c	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-11 10:42:38.834325+00	
********-0000-0000-0000-************	bb059115-e985-44ad-8570-329adf05950e	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-11 10:42:38.843642+00	
********-0000-0000-0000-************	df499976-2eba-4055-92ea-347401109227	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-11 20:48:52.179526+00	
********-0000-0000-0000-************	95280989-a6e6-4ce1-8a1f-24b7b4c1d5cf	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-11 20:48:52.189359+00	
********-0000-0000-0000-************	a7903538-1d17-4dd7-b0ae-f55c2a56ef22	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-12 09:37:54.588569+00	
********-0000-0000-0000-************	74288d68-f6e1-4c73-bcca-12f8c2db1100	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-12 09:37:54.595633+00	
********-0000-0000-0000-************	8d50fcb5-aa02-472a-9cef-db62964fbe84	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-12 10:38:51.53741+00	
********-0000-0000-0000-************	3fbee4d6-5be9-400d-afe4-f462befdc97d	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-12 10:38:51.540822+00	
********-0000-0000-0000-************	223eee71-6a3c-4a1e-8d72-68e3a27aa6d6	{"action":"login","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-13 00:43:05.16218+00	
********-0000-0000-0000-************	4f5789d3-e87a-46a3-8cfb-7801eae1d24a	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-13 00:45:02.95355+00	
********-0000-0000-0000-************	6a674572-6dda-4679-a9c0-7a5ee06ad252	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-13 00:45:02.954445+00	
********-0000-0000-0000-************	effcc998-bd78-4cda-9643-b7d8873f1625	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-13 10:24:28.00041+00	
********-0000-0000-0000-************	3bdd72c5-de44-41ca-83b5-61a1f022b31c	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-13 10:24:28.013198+00	
********-0000-0000-0000-************	76321bf6-1a96-4800-b536-9a46476282a5	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-14 06:41:24.200275+00	
********-0000-0000-0000-************	61180987-4471-4353-996b-ddd783ab599d	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-14 06:41:24.213004+00	
********-0000-0000-0000-************	52235a2f-0935-4756-8701-8716039686a9	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-14 08:20:49.664563+00	
********-0000-0000-0000-************	697cea6b-04e6-4581-833c-be5a810a55e4	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-14 08:20:49.665419+00	
********-0000-0000-0000-************	e682bbe3-1bd3-422e-acd2-4b70716aa175	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-14 10:08:49.521167+00	
********-0000-0000-0000-************	8a00b8b6-9e7e-4eb3-93c4-9beabaf4046a	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-14 10:08:49.528447+00	
********-0000-0000-0000-************	d2e9b635-c015-4c72-9a28-3709b503e296	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-14 11:36:12.065207+00	
********-0000-0000-0000-************	46d4089e-1afc-4ded-be15-3e01bb7dce4f	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-14 11:36:12.085093+00	
********-0000-0000-0000-************	6f47e59e-8201-4b28-827c-3e871cd13885	{"action":"login","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-14 14:33:01.380271+00	
********-0000-0000-0000-************	354b5d99-16fd-4ff4-a4a2-1ca717a9deb7	{"action":"user_signedup","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"b4a0703c-35b3-424e-aaea-ad886058e7c6","user_phone":""}}	2025-06-14 14:34:12.332261+00	
********-0000-0000-0000-************	237931b9-072d-4776-84a7-0da307edd4ec	{"action":"logout","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-06-14 14:34:18.185316+00	
********-0000-0000-0000-************	4a1327b6-28a6-4214-89f9-66d4fd505141	{"action":"login","actor_id":"b4a0703c-35b3-424e-aaea-ad886058e7c6","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-14 14:34:20.660126+00	
********-0000-0000-0000-************	3ee80c11-87ee-4b41-ac8c-c599b68ae36f	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"b4a0703c-35b3-424e-aaea-ad886058e7c6","user_phone":""}}	2025-06-14 15:31:13.172608+00	
********-0000-0000-0000-************	938744cb-5716-4e98-b736-eedf7975cefb	{"action":"login","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-15 00:30:18.338752+00	
********-0000-0000-0000-************	1f7aca1c-e597-423a-b7af-75283e3c360a	{"action":"logout","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-06-15 00:53:48.760914+00	
********-0000-0000-0000-************	902344ec-e084-4a68-88ca-32ab197c7680	{"action":"login","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-15 00:53:54.358553+00	
********-0000-0000-0000-************	d5a627e7-d088-46e5-af63-b21a7ce60e2b	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 01:52:05.495286+00	
********-0000-0000-0000-************	41ad52d6-5643-4745-8d65-99d4002b901f	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 01:52:05.496557+00	
********-0000-0000-0000-************	c69d4191-eef4-4250-b337-f3caaa0c7752	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 05:41:43.23739+00	
********-0000-0000-0000-************	d81b8fb3-6ff1-4f71-bf34-31e4a851f60a	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 05:41:43.238383+00	
********-0000-0000-0000-************	ecc49315-0457-488b-916d-1371767d0904	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 07:53:36.008034+00	
********-0000-0000-0000-************	0bce154f-c8ef-490a-b3ea-d5257f45dd85	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 07:53:36.009051+00	
********-0000-0000-0000-************	02d1c03b-8332-4a4e-981e-2ca81a48bc5c	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 08:52:10.708364+00	
********-0000-0000-0000-************	cdb9b3eb-9cc3-4be6-9ee4-cb2f4d178e59	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 08:52:10.710575+00	
********-0000-0000-0000-************	fd805fd0-51e9-46d0-8919-e8d905f6bb41	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 09:50:54.494182+00	
********-0000-0000-0000-************	3559753e-3840-4058-982a-ce1851e1426f	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 09:50:54.495569+00	
********-0000-0000-0000-************	bac9a3fa-22b1-4a4a-8faa-d986fda24522	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 11:11:01.564115+00	
********-0000-0000-0000-************	f7550a5a-dbf2-469a-a185-f6a97c78811a	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 11:11:01.565111+00	
********-0000-0000-0000-************	8e29daf0-5f63-4eab-8d35-74454d810611	{"action":"login","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-15 12:12:34.829603+00	
********-0000-0000-0000-************	f8037585-8316-4c5a-b83b-f79f6b467ca7	{"action":"login","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-15 12:19:57.05364+00	
********-0000-0000-0000-************	e77398f0-8edc-407c-bc7c-442b4a1d83be	{"action":"login","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-15 13:05:23.385304+00	
********-0000-0000-0000-************	********-0b0b-4992-9403-eeeb88cfd1f5	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 13:10:49.543408+00	
********-0000-0000-0000-************	9dc93a97-bc6c-47f6-97a0-9a792d158996	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 13:10:49.546349+00	
********-0000-0000-0000-************	7e3cc04a-b6f7-4724-848f-71651cecbdf3	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 13:12:10.614957+00	
********-0000-0000-0000-************	e9544e23-6d35-4fea-b133-328391461e15	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 13:12:10.616939+00	
********-0000-0000-0000-************	1c93c87f-626f-41b7-a568-f14822f8ab07	{"action":"login","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-15 13:29:52.330901+00	
********-0000-0000-0000-************	5088f244-ad01-4f76-968b-fcfc9aed5493	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 14:20:23.577765+00	
********-0000-0000-0000-************	0740fd39-0fcf-4d4e-90d7-a3141e9ad8f5	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 14:20:23.580347+00	
********-0000-0000-0000-************	5bfeed03-8424-41f7-9681-814a2041a61b	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 14:26:43.709338+00	
********-0000-0000-0000-************	45f66b6d-6a7a-4447-ae8e-5c9e23e9db13	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 14:26:43.710892+00	
********-0000-0000-0000-************	acfce274-ec95-4572-b0eb-777fa8ee42c9	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 14:29:06.240618+00	
********-0000-0000-0000-************	2e13ea87-c8cc-454b-8378-1e75b446aaaa	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 14:29:06.24204+00	
********-0000-0000-0000-************	ff772a81-d7aa-41dd-b7ac-8b17bc4325ea	{"action":"login","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-15 14:52:08.874759+00	
********-0000-0000-0000-************	ee7cda11-1510-423b-8e18-d0e9ff6c7842	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 15:24:45.699+00	
********-0000-0000-0000-************	529fc884-bc8d-4bfc-b9b2-24b054fc9842	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 15:24:45.700331+00	
********-0000-0000-0000-************	93acd484-c9d6-4e12-87e8-ec3f1a33c65a	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 15:26:58.520008+00	
********-0000-0000-0000-************	6ae13f8c-78a6-4128-a1b4-1b6a04683071	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 15:26:58.521136+00	
********-0000-0000-0000-************	adc3829d-5214-41d7-a06e-1835390a6b26	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 17:27:51.264042+00	
********-0000-0000-0000-************	7cbe7d75-b155-4ab9-943e-0df98ff9a6de	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 17:27:51.265772+00	
********-0000-0000-0000-************	7504d06f-1110-4737-813a-cc78bff7f5e1	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-16 00:30:36.565888+00	
********-0000-0000-0000-************	068dd850-7d34-46a2-8a99-b1ce512976c6	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-16 00:30:36.568354+00	
********-0000-0000-0000-************	46abc340-618b-4b52-b932-48c98273fdfb	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-16 02:34:26.369821+00	
********-0000-0000-0000-************	a1b6801e-4070-43d0-b31b-27bc9129825b	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-16 02:34:26.371925+00	
********-0000-0000-0000-************	d543132e-28e0-4bff-982a-40ee22e21541	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-16 03:32:35.2149+00	
********-0000-0000-0000-************	2d4ab280-edf9-4ee4-8270-875e193cd0a1	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-16 03:32:35.216383+00	
********-0000-0000-0000-************	f8524f40-3130-483c-9fca-78d2aa96286e	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-16 04:30:41.702554+00	
********-0000-0000-0000-************	b2e8cd27-bd49-44d1-a547-b1b75972efee	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-16 04:30:41.705658+00	
********-0000-0000-0000-************	4efd82ff-5d8e-4921-ba6f-d4708e882b51	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-16 05:37:26.969877+00	
********-0000-0000-0000-************	f85b5ca5-831d-4bc4-9d1e-5dc364077b6e	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-16 05:37:26.977051+00	
********-0000-0000-0000-************	23ab8143-15a3-45cf-ad60-7a94c9181b6e	{"action":"login","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-17 09:38:04.233421+00	
********-0000-0000-0000-************	082626d1-a8ad-4a33-a241-6e51692a029a	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","user_phone":""}}	2025-06-17 09:38:13.856208+00	
********-0000-0000-0000-************	********-c7fd-40f3-bddc-962bfe875554	{"action":"user_confirmation_requested","actor_id":"72ee36ce-4e0c-433a-b9e4-379ca26e9e2f","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-06-17 10:06:20.899291+00	
********-0000-0000-0000-************	c3ed11af-9d3c-452c-9a80-55cf8e5533ed	{"action":"user_confirmation_requested","actor_id":"cf71d586-b5e9-4d7f-8d8c-c7a1fdf0a4b9","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-06-17 10:16:26.902289+00	
********-0000-0000-0000-************	65ef2747-c70b-47c5-8051-0752e553aeec	{"action":"user_signedup","actor_id":"cf71d586-b5e9-4d7f-8d8c-c7a1fdf0a4b9","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"team"}	2025-06-17 10:16:59.668782+00	
********-0000-0000-0000-************	612cd087-1562-45db-8f6f-e6c4daf3b899	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"72ee36ce-4e0c-433a-b9e4-379ca26e9e2f","user_phone":""}}	2025-06-17 10:18:44.752284+00	
********-0000-0000-0000-************	85d796ca-b019-48e9-9a35-fd5eec3f5ba8	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"cf71d586-b5e9-4d7f-8d8c-c7a1fdf0a4b9","user_phone":""}}	2025-06-17 10:19:02.797444+00	
********-0000-0000-0000-************	ffa36b8c-7c5e-4787-b477-9c518738282b	{"action":"user_confirmation_requested","actor_id":"9593be09-f3f5-455b-b4c0-b5b5ef09ef13","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-06-17 10:19:13.741897+00	
********-0000-0000-0000-************	5f9d919a-5ea1-4e9d-8cc7-f2458d7c408b	{"action":"user_signedup","actor_id":"9593be09-f3f5-455b-b4c0-b5b5ef09ef13","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"team"}	2025-06-17 10:19:25.809927+00	
********-0000-0000-0000-************	6eab9ee3-c0ba-4393-960d-f074e7bbf21a	{"action":"user_confirmation_requested","actor_id":"7e960dd3-190b-4e41-b4f6-69ed62e6c54b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-06-17 10:20:03.759189+00	
********-0000-0000-0000-************	f70032bd-c13a-4aa1-bf70-1ad24f6e0148	{"action":"token_refreshed","actor_id":"9593be09-f3f5-455b-b4c0-b5b5ef09ef13","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-17 11:18:30.792092+00	
********-0000-0000-0000-************	ac0eace7-7480-4c4d-950d-58d09bed033e	{"action":"token_revoked","actor_id":"9593be09-f3f5-455b-b4c0-b5b5ef09ef13","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-17 11:18:30.79771+00	
********-0000-0000-0000-************	867421b3-d63c-432a-80d9-69b875d5d2f0	{"action":"token_refreshed","actor_id":"9593be09-f3f5-455b-b4c0-b5b5ef09ef13","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-17 12:17:03.750674+00	
********-0000-0000-0000-************	ccce2fcb-2237-4eaa-9257-8a707fd0848a	{"action":"token_revoked","actor_id":"9593be09-f3f5-455b-b4c0-b5b5ef09ef13","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-17 12:17:03.753577+00	
********-0000-0000-0000-************	2d6bbb2b-02eb-4a18-ae68-ae8d39487042	{"action":"token_refreshed","actor_id":"9593be09-f3f5-455b-b4c0-b5b5ef09ef13","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-17 15:48:04.001459+00	
********-0000-0000-0000-************	35a05d98-1ac1-4eb2-a376-4560ab82f7e4	{"action":"token_revoked","actor_id":"9593be09-f3f5-455b-b4c0-b5b5ef09ef13","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-17 15:48:04.021997+00	
********-0000-0000-0000-************	abb9d3c8-872f-44b6-843c-6768bc446132	{"action":"login","actor_id":"9593be09-f3f5-455b-b4c0-b5b5ef09ef13","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-17 15:48:08.220376+00	
********-0000-0000-0000-************	1e41481d-c45c-4419-bdd2-ef0a5e3da6bd	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"9593be09-f3f5-455b-b4c0-b5b5ef09ef13","user_phone":""}}	2025-06-17 16:22:03.711209+00	
********-0000-0000-0000-************	6a0968ad-5973-4c50-9ecb-5ddf314091f5	{"action":"user_confirmation_requested","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-06-17 16:22:20.14148+00	
********-0000-0000-0000-************	0c00a8e2-bb04-4fad-b07f-626ebfa32c35	{"action":"user_signedup","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"team"}	2025-06-17 16:22:33.719386+00	
********-0000-0000-0000-************	a608a8b3-9bc3-46cd-a93e-87d76013e6c4	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-17 16:22:39.237673+00	
********-0000-0000-0000-************	57cb12a1-ea5c-477c-84e8-a407385544c1	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"7e960dd3-190b-4e41-b4f6-69ed62e6c54b","user_phone":""}}	2025-06-18 00:26:56.226143+00	
********-0000-0000-0000-************	c728d7bd-77a3-4da5-b6a7-c176551636d0	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-18 03:38:28.269681+00	
********-0000-0000-0000-************	b96c276a-c642-41b1-9d43-934c530fac48	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-18 03:40:10.589356+00	
********-0000-0000-0000-************	49e6370f-c07b-4ed3-9739-1eb7a4cce246	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-18 03:40:40.420176+00	
********-0000-0000-0000-************	1b6bb7c9-1ac0-41e3-84ad-dd4f1466ee61	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-18 04:42:18.198278+00	
********-0000-0000-0000-************	1f8134a2-09b4-465d-8bb1-3762667e8d43	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-18 04:43:48.762941+00	
********-0000-0000-0000-************	71581f8c-4f76-49fc-8831-a43d32a421f0	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-18 04:46:25.456128+00	
********-0000-0000-0000-************	228ff4a4-e0c5-4b42-86aa-992c7d13452f	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-18 04:52:37.602444+00	
********-0000-0000-0000-************	a8def8ec-423d-424f-8fed-0005cbd48deb	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-18 05:09:19.831031+00	
********-0000-0000-0000-************	78ed945a-ee84-49d6-a02f-41f19afc8b5a	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 06:19:48.608256+00	
********-0000-0000-0000-************	f3bff03c-3cd4-4458-9af7-fe37d13ba362	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 06:19:48.611989+00	
********-0000-0000-0000-************	9ed06b81-a517-4626-8207-86214221c1b9	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 07:19:33.932118+00	
********-0000-0000-0000-************	a24e0aab-cb45-4263-bd6f-c49e046833a7	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 07:19:33.934249+00	
********-0000-0000-0000-************	0a062f3b-75c7-4ac4-8e16-06f8ae5bf72a	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 08:37:27.605794+00	
********-0000-0000-0000-************	2f146873-f3c7-42ba-a645-dad27e976226	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 08:37:27.607223+00	
********-0000-0000-0000-************	73116942-1a05-4266-9f37-fb15aa1fb5b7	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 09:36:58.21853+00	
********-0000-0000-0000-************	04ced8f1-7b15-4fc4-a70e-e00d5e91b38a	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 09:36:58.220933+00	
********-0000-0000-0000-************	ca3edce1-1ba1-4f51-ac90-6f2628105470	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 16:14:54.284405+00	
********-0000-0000-0000-************	7ad0d9a4-b84b-421c-9dbb-02f67201eb1c	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 16:14:54.304282+00	
********-0000-0000-0000-************	b7e1b532-1b83-4196-93fb-333d3091077e	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 17:13:36.371695+00	
********-0000-0000-0000-************	5dc3d7fc-2713-4453-8d47-76e857517b69	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 17:13:36.37544+00	
********-0000-0000-0000-************	48a85885-ce45-4ccd-8ae8-5b93f50c25bc	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 20:19:59.293257+00	
********-0000-0000-0000-************	e634b2f1-145d-4f35-8909-7e28392bb5df	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 20:19:59.303407+00	
********-0000-0000-0000-************	b01d462c-87b9-4eea-a292-c75efb208fbb	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 02:25:35.107273+00	
********-0000-0000-0000-************	ff97451d-fedd-40a3-b65f-0f87fb60f31c	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 02:25:35.117625+00	
********-0000-0000-0000-************	5a891525-ff48-43d4-baa9-6938c690d42e	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 03:23:49.586842+00	
********-0000-0000-0000-************	e024dc3d-072d-4a00-9fe1-e2f6bc469f80	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 03:23:49.590432+00	
********-0000-0000-0000-************	53b6ae03-92ab-4631-a33e-f6c52fa4c62a	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 04:49:31.024942+00	
********-0000-0000-0000-************	9ca200dc-0124-481a-9dce-b8ef994d6970	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 04:49:31.033004+00	
********-0000-0000-0000-************	c2cb6724-7f9d-46d5-b72f-db46ec438f34	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-06-19 04:49:36.429638+00	
********-0000-0000-0000-************	50f521b4-702a-42de-b5c8-c529f538f610	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-19 04:49:41.599975+00	
********-0000-0000-0000-************	ae199886-ad04-4a27-86a0-d756b59a13ed	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 06:28:35.966455+00	
********-0000-0000-0000-************	8123d5ae-a0ad-43ab-8623-c277ac3425db	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 06:28:35.969648+00	
********-0000-0000-0000-************	b9f0ed7e-b567-4c45-98ef-63a13f9603a9	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 07:27:11.434037+00	
********-0000-0000-0000-************	aa7439c5-8aa9-416d-bafc-32b78b81313a	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 07:27:11.436882+00	
********-0000-0000-0000-************	7594a8c2-98af-47d4-9a27-c00fc84042bf	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 09:36:57.82196+00	
********-0000-0000-0000-************	e2186654-172d-40ba-9791-8dde38537ee8	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 09:36:57.830936+00	
********-0000-0000-0000-************	d7a542b6-14d1-4b69-913b-46f57935bdde	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 10:35:30.524071+00	
********-0000-0000-0000-************	8d6b5c5a-9fb4-433c-9cfd-898de39150d5	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 10:35:30.526412+00	
********-0000-0000-0000-************	43eed376-06ea-4938-917f-5342ea0b8772	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 11:46:08.180731+00	
********-0000-0000-0000-************	65726a24-e842-42b2-88f7-9d0c02c91a12	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 11:46:08.183298+00	
********-0000-0000-0000-************	********-32b6-4b12-afea-f28bba559f17	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-19 12:33:53.144896+00	
********-0000-0000-0000-************	8493a5cc-d8cb-4537-9ef1-37dcfcbd7a91	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-06-19 12:37:59.873046+00	
********-0000-0000-0000-************	da951d37-5991-44c8-ba31-feb2cc13b497	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-19 12:38:04.758649+00	
********-0000-0000-0000-************	********-9f6c-42ef-b810-82c5cb9ef8ed	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-06-19 12:38:08.518352+00	
********-0000-0000-0000-************	17df229a-5461-498b-9b90-0b03cff0bf2c	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-19 12:39:24.201605+00	
********-0000-0000-0000-************	3d507fff-9452-4975-9308-f4dfbc03e725	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-19 12:40:06.719862+00	
********-0000-0000-0000-************	b2304539-2c4c-435c-b5ea-78e1fbed0d45	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-06-19 12:40:17.635907+00	
********-0000-0000-0000-************	b240c1ed-8f5d-43b4-8edb-ae8823a4f3c4	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-20 00:02:13.927482+00	
********-0000-0000-0000-************	47041bb0-5242-422c-868c-161be540a601	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-20 01:01:47.147652+00	
********-0000-0000-0000-************	fe92bef0-c283-4ee9-bbda-12974f27a319	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-20 01:01:47.150476+00	
********-0000-0000-0000-************	95d40bfc-b84f-490c-8192-e7ca4e4ceadc	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-18 08:05:56.812258+00	
********-0000-0000-0000-************	b449c0d2-4ed1-41db-a403-8c588f8e5cc3	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-18 08:12:00.564394+00	
********-0000-0000-0000-************	7beb55f1-9caa-45ab-b7f4-5659966bfa10	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-18 08:15:09.376264+00	
********-0000-0000-0000-************	1c953078-d077-45ca-96b6-59e6814cc28e	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-19 04:14:29.887569+00	
********-0000-0000-0000-************	6db79546-2d09-4a38-a8cb-61321333cda6	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-19 04:14:29.908415+00	
********-0000-0000-0000-************	15ccd53d-de50-4b3f-8f40-26f8ce5da72f	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-19 04:14:44.881371+00	
********-0000-0000-0000-************	c1077df5-da8d-44af-83fb-f7557efff4d8	{"action":"user_confirmation_requested","actor_id":"cac9897f-3814-4a5c-8890-79b2e35c52a8","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 04:14:54.992032+00	
********-0000-0000-0000-************	afda2a7c-658e-4972-a3ad-27c1f5c69f73	{"action":"user_signedup","actor_id":"cac9897f-3814-4a5c-8890-79b2e35c52a8","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"team"}	2025-07-19 04:15:50.603201+00	
********-0000-0000-0000-************	387012db-93dc-4e5e-8f2c-3da27d215d20	{"action":"logout","actor_id":"cac9897f-3814-4a5c-8890-79b2e35c52a8","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-19 04:23:54.679434+00	
********-0000-0000-0000-************	94f9d626-6129-4a35-a817-9ee5d09191ed	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-19 04:24:01.163263+00	
********-0000-0000-0000-************	3bd079d8-1eba-4f54-8c58-191de17fa6e5	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-19 05:30:34.656716+00	
********-0000-0000-0000-************	cdadea53-feed-4dd2-8697-f7ac1bf9d3a6	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-19 05:30:34.66559+00	
********-0000-0000-0000-************	013a8d65-2e34-42ff-86c6-faca616e270c	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-19 06:06:30.195413+00	
********-0000-0000-0000-************	43ed51d2-25dd-44ae-8e69-35bcadaa87da	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"cac9897f-3814-4a5c-8890-79b2e35c52a8","user_phone":""}}	2025-07-19 06:08:09.688961+00	
********-0000-0000-0000-************	55f651f0-1ade-46a4-b0b9-95c522c5d0b8	{"action":"user_confirmation_requested","actor_id":"6e029114-7c26-4dd7-a5e8-4cfe0f16ffaf","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:08:35.014848+00	
********-0000-0000-0000-************	3ca09e7b-b35c-491d-a6d5-d47151ce637c	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"6e029114-7c26-4dd7-a5e8-4cfe0f16ffaf","user_phone":""}}	2025-07-19 06:10:54.694761+00	
********-0000-0000-0000-************	ab6c0114-58df-4c70-8385-7e44c6564fdf	{"action":"user_confirmation_requested","actor_id":"27ba2b34-328a-4e06-919a-3f97cb46fd30","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:11:52.101419+00	
********-0000-0000-0000-************	6255dac7-ba84-4df2-847e-174d47aae913	{"action":"user_signedup","actor_id":"27ba2b34-328a-4e06-919a-3f97cb46fd30","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"team"}	2025-07-19 06:12:11.403672+00	
********-0000-0000-0000-************	299e338a-ea8e-449f-aff7-cdde810f6bf9	{"action":"logout","actor_id":"27ba2b34-328a-4e06-919a-3f97cb46fd30","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-19 06:15:31.236383+00	
********-0000-0000-0000-************	0fad88d1-954b-4786-8c99-86bd4cc412c8	{"action":"user_repeated_signup","actor_id":"27ba2b34-328a-4e06-919a-3f97cb46fd30","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:15:37.071983+00	
********-0000-0000-0000-************	f38693f3-b8bc-461f-888a-23db40ccf896	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"27ba2b34-328a-4e06-919a-3f97cb46fd30","user_phone":""}}	2025-07-19 06:15:59.338521+00	
********-0000-0000-0000-************	57d8147d-41d0-4ddf-862e-da4ac4d837a2	{"action":"user_confirmation_requested","actor_id":"ea6c9640-8770-4ea8-ae82-5e2a178245d5","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:16:02.145911+00	
********-0000-0000-0000-************	99b6b512-a46d-47d1-a0d4-b43e91b21f91	{"action":"user_confirmation_requested","actor_id":"ea6c9640-8770-4ea8-ae82-5e2a178245d5","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:17:47.296616+00	
********-0000-0000-0000-************	9dc44049-0d8e-4ad4-947f-d1a33b92bde5	{"action":"user_signedup","actor_id":"ea6c9640-8770-4ea8-ae82-5e2a178245d5","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"team"}	2025-07-19 06:18:43.646055+00	
********-0000-0000-0000-************	e816c437-207c-4945-b57b-5be8f1476a83	{"action":"logout","actor_id":"ea6c9640-8770-4ea8-ae82-5e2a178245d5","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-19 06:20:39.5893+00	
********-0000-0000-0000-************	9ed69ad7-ec92-452e-841c-67a4c9ab7aed	{"action":"user_repeated_signup","actor_id":"ea6c9640-8770-4ea8-ae82-5e2a178245d5","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:20:48.323036+00	
********-0000-0000-0000-************	4dc04888-d087-44a5-8a3b-dfb623b31a22	{"action":"user_repeated_signup","actor_id":"ea6c9640-8770-4ea8-ae82-5e2a178245d5","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:30:28.681872+00	
********-0000-0000-0000-************	a86cddd4-d1f3-4725-979f-b946c2bf01c8	{"action":"user_repeated_signup","actor_id":"ea6c9640-8770-4ea8-ae82-5e2a178245d5","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:30:36.785145+00	
********-0000-0000-0000-************	195ac2ec-4800-450b-b83e-6e1137c386ac	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-19 06:31:40.979487+00	
********-0000-0000-0000-************	40eb83bf-f0e1-4f81-9c5e-2b55aaf39d3b	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-19 06:31:46.883554+00	
********-0000-0000-0000-************	ac803cd2-e190-440d-9400-0e12ca9aa6bd	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"ea6c9640-8770-4ea8-ae82-5e2a178245d5","user_phone":""}}	2025-07-19 06:31:55.883455+00	
********-0000-0000-0000-************	91fed1e2-1c03-441d-911b-eb9845ad6f74	{"action":"user_confirmation_requested","actor_id":"5dbe8b46-856d-48d8-ba4b-086ac2bcea4a","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:32:05.376613+00	
********-0000-0000-0000-************	5919fe64-bf44-4352-998a-8d9ad8942fa9	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"5dbe8b46-856d-48d8-ba4b-086ac2bcea4a","user_phone":""}}	2025-07-19 06:35:05.337698+00	
********-0000-0000-0000-************	29f796fd-3941-4ce9-925f-a7c9fb6353ad	{"action":"user_confirmation_requested","actor_id":"a364b8e7-dbca-4b58-a3e6-ad2f24684931","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:35:11.945348+00	
********-0000-0000-0000-************	ff863975-2156-49c7-9735-8d1bc1a8daac	{"action":"user_repeated_signup","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:35:55.423094+00	
********-0000-0000-0000-************	46b9d010-3d2c-4a7b-96d7-302b8069bdd7	{"action":"user_confirmation_requested","actor_id":"a364b8e7-dbca-4b58-a3e6-ad2f24684931","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:38:12.661703+00	
********-0000-0000-0000-************	36e4ff9a-9120-4a61-b86a-a4267ccf1622	{"action":"user_confirmation_requested","actor_id":"a364b8e7-dbca-4b58-a3e6-ad2f24684931","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:39:56.301387+00	
********-0000-0000-0000-************	07731f49-c7ef-4393-8532-9eeb6d8b5fd5	{"action":"user_confirmation_requested","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:41:32.545455+00	
********-0000-0000-0000-************	2774d954-c741-4143-88b4-ad4415acd72a	{"action":"user_confirmation_requested","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:42:34.560392+00	
********-0000-0000-0000-************	2ad15a82-ab14-48e3-8435-2b51cbd71f23	{"action":"user_confirmation_requested","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user"}	2025-07-19 06:43:43.849419+00	
********-0000-0000-0000-************	fa9cd60f-7e6d-4cd6-8bd3-64d002e65722	{"action":"user_signedup","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"team"}	2025-07-19 06:43:57.902736+00	
********-0000-0000-0000-************	8b74f2be-b402-49a2-9cbd-61c6a7c6d44c	{"action":"token_refreshed","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-19 09:55:21.966346+00	
********-0000-0000-0000-************	a507f4ab-b7ec-4be7-8401-6d9bbe55624c	{"action":"token_revoked","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-19 09:55:22.240999+00	
********-0000-0000-0000-************	0932ba9c-9609-475d-af76-e30c8a627871	{"action":"logout","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-19 09:57:13.904658+00	
********-0000-0000-0000-************	3d2a9515-7a16-4db8-9bc1-60dde9d9c4fe	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-19 09:57:17.88589+00	
********-0000-0000-0000-************	ce163596-69b6-4424-b5e9-a0ba290456f1	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-19 14:40:33.119276+00	
********-0000-0000-0000-************	ae9adad1-d8e0-4b3d-9a6d-fc437b2a4ddf	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-19 14:40:33.129095+00	
********-0000-0000-0000-************	14e9e329-fe24-48b6-ba5e-e1008c504c6b	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-19 16:27:53.83693+00	
********-0000-0000-0000-************	e5c2206a-646b-4551-9041-72436518750c	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-19 16:27:53.848255+00	
********-0000-0000-0000-************	86b33c31-0244-4b8d-84fd-d7c2829a53f4	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-19 17:26:39.840266+00	
********-0000-0000-0000-************	7122e278-d5e3-4df8-b6fc-72d55be3285e	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-19 17:26:39.84393+00	
********-0000-0000-0000-************	17056bde-6479-42cd-a11f-81e8d8251a16	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-20 01:05:20.031541+00	
********-0000-0000-0000-************	effd3021-743a-4157-9480-abfa57257da3	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-20 01:05:20.048356+00	
********-0000-0000-0000-************	a954b620-4775-4b36-8e21-fd119b7ac8b0	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-22 01:42:51.702333+00	
********-0000-0000-0000-************	899cad8f-2799-48ef-a7e7-0a3397d152e6	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-22 01:42:51.713954+00	
********-0000-0000-0000-************	e2f34407-fc24-4f91-b5a0-962224616d4d	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-22 04:22:11.935373+00	
********-0000-0000-0000-************	88b8b5ba-a0a1-4c10-b993-6a5e8a305535	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-22 04:22:11.938891+00	
********-0000-0000-0000-************	c32f82ef-1660-4f1a-9dc4-febf5183b77e	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-22 05:21:45.904527+00	
********-0000-0000-0000-************	9c473525-933c-4fb9-b161-2245dcccc708	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-22 05:21:45.910591+00	
********-0000-0000-0000-************	8ab01a96-7c47-46bd-8cf6-7682509d9ed5	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-22 06:21:27.205527+00	
********-0000-0000-0000-************	768f7e12-f5c8-4d9c-989a-b8541f0716d1	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-22 06:21:27.218148+00	
********-0000-0000-0000-************	79f54cde-834f-4f67-8215-608a31e533e9	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-22 07:20:09.077369+00	
********-0000-0000-0000-************	72877034-fa80-4b91-a5da-ef1935cebf16	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-22 07:20:09.080203+00	
********-0000-0000-0000-************	5731f27c-f7ea-45a9-997d-f7c3ff9fa8b3	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-24 02:40:38.97482+00	
********-0000-0000-0000-************	0e9e8e3e-9124-44d0-8c6c-d6fafc048475	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-24 02:40:38.984078+00	
********-0000-0000-0000-************	ad501f29-3a23-4ed0-96dd-129688e7044b	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-24 03:19:53.620444+00	
********-0000-0000-0000-************	c2ed3b5d-cb53-4b95-b35e-4ee8c6308e75	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-24 04:19:07.525455+00	
********-0000-0000-0000-************	eb3a52e4-fef4-460c-bdec-************	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-24 04:19:07.528513+00	
********-0000-0000-0000-************	7b2e9357-83c4-4b38-9ad3-b1da3d365171	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-24 05:18:55.845963+00	
********-0000-0000-0000-************	940d801f-390d-49a9-bdeb-12aa82137b7a	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-24 05:18:55.850456+00	
********-0000-0000-0000-************	edfe543c-46de-48e7-be90-c68bd308b42b	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-24 05:20:25.52825+00	
********-0000-0000-0000-************	f6acf915-4b8d-41d4-9c3a-bbac96a787f7	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-24 05:20:25.529358+00	
********-0000-0000-0000-************	2e92ce57-22d1-4826-9bfd-a42bfc4ddbc2	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 02:00:26.940443+00	
********-0000-0000-0000-************	98758d60-3886-4ab8-994e-f0e97f03dfec	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 02:00:26.955352+00	
********-0000-0000-0000-************	8012ca84-ae5c-4e8e-8801-8933fe28e452	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 02:51:02.553253+00	
********-0000-0000-0000-************	34158385-6abe-4e43-9b2f-5d1fa0d09fe0	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 02:51:02.566068+00	
********-0000-0000-0000-************	0e563650-e032-4d6f-b085-1166da7c6766	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 03:50:10.131095+00	
********-0000-0000-0000-************	b6649c1d-5311-4789-b2a0-ccee22bf2e04	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 03:50:10.134117+00	
********-0000-0000-0000-************	d98521bc-0ba0-47ec-82d1-673fa618863d	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 06:26:10.513977+00	
********-0000-0000-0000-************	150b56ed-b1d0-47ef-b7da-ecc4176efd47	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 06:26:10.523619+00	
********-0000-0000-0000-************	ac8cad3d-daea-466e-a170-1abd4dd3eae6	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 07:56:29.805975+00	
********-0000-0000-0000-************	82eb831d-3046-44c1-8069-b4ae6db6b25b	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 07:56:29.81174+00	
********-0000-0000-0000-************	801e2dce-2bd5-467a-a7c2-505d152583eb	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 08:54:41.626568+00	
********-0000-0000-0000-************	e6be564f-ea33-4b99-9f0c-0c15e28e1758	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 08:54:41.629208+00	
********-0000-0000-0000-************	be603f15-1dae-465c-b2a1-6455d7229e70	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 10:26:05.728804+00	
********-0000-0000-0000-************	3c4bf075-11ac-4466-a092-174db9bf96f9	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 10:26:05.735511+00	
********-0000-0000-0000-************	7c9462aa-328d-4076-9d6d-5f70345c0f8c	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 13:15:34.640973+00	
********-0000-0000-0000-************	86abbcac-366c-4603-a6bb-0745c8055237	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 13:15:34.653616+00	
********-0000-0000-0000-************	425db652-b0b9-41d2-8b30-6ed3425c6237	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 14:41:47.143758+00	
********-0000-0000-0000-************	781b059e-7be8-42ee-bbed-62975a702ca1	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 14:41:47.171345+00	
********-0000-0000-0000-************	6204a742-7223-47ac-b049-be75006e787c	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 15:52:20.754684+00	
********-0000-0000-0000-************	4ab964a6-54b5-4037-8250-bc39a9ff804f	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 15:52:20.757933+00	
********-0000-0000-0000-************	f146a5bf-8216-4b4b-8030-57c28dbe36f3	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 01:12:28.885474+00	
********-0000-0000-0000-************	e0be6c7c-06c4-4fed-bf1b-d2eb66f78681	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 01:12:28.900273+00	
********-0000-0000-0000-************	22b4f28e-d6e1-4346-8024-d4dc3f87a076	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 03:54:55.089296+00	
********-0000-0000-0000-************	dacacb75-5fe0-460c-8191-238c2b835349	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 03:54:55.094303+00	
********-0000-0000-0000-************	84ee2f7a-6e7a-4362-88d3-373aebdf566e	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 06:37:46.019891+00	
********-0000-0000-0000-************	75ff1dbe-84a4-435d-92af-6727427d712e	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 06:37:46.022039+00	
********-0000-0000-0000-************	57dd56a6-c5fd-4b94-bfb7-d77e4103b412	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 07:44:44.220135+00	
********-0000-0000-0000-************	3a25890a-ff2c-46d2-9ac0-5247fbee02d4	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 07:44:44.221998+00	
********-0000-0000-0000-************	70c18382-78a6-4b1e-bb79-4638ea8a4d60	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 07:50:53.628723+00	
********-0000-0000-0000-************	30b6a925-1eba-4a4a-b31f-8d4553224c5e	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 07:50:53.631506+00	
********-0000-0000-0000-************	28a3f881-44fe-40e0-9d0d-3154542965c9	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 08:49:21.657199+00	
********-0000-0000-0000-************	f1a51159-b37a-4726-a1ca-12ee8a1e26db	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 08:49:21.659388+00	
********-0000-0000-0000-************	39a3474b-a8bc-4c6f-8c4b-b5829ebdc26d	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 09:50:36.966466+00	
********-0000-0000-0000-************	7cff0ae1-e1f5-48f4-8402-fcc7c8d044b7	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 09:50:36.973352+00	
********-0000-0000-0000-************	4c75ed7a-b795-4777-8b29-033e0bbc8713	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 10:49:04.047533+00	
********-0000-0000-0000-************	2970589a-a849-4978-a8d3-89abb8c61640	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 10:49:04.051293+00	
********-0000-0000-0000-************	4546553f-7e40-48f9-a2d9-8ae4bcaae429	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 11:47:28.948204+00	
********-0000-0000-0000-************	5ba67d4d-2163-42d4-b625-35072d099be7	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 11:47:28.950699+00	
********-0000-0000-0000-************	20ff1550-ec49-402d-b6e0-71d72be77134	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 15:56:52.635417+00	
********-0000-0000-0000-************	b29bf21b-98b6-4c36-afa3-7e725b4e9733	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 15:56:52.650305+00	
********-0000-0000-0000-************	f7a8db63-c42d-49ab-a318-7e71ea2f5b45	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-27 18:36:22.4073+00	
********-0000-0000-0000-************	f2ab6bc8-c4d7-4d49-9bdc-4b780189f54a	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-27 18:36:22.42236+00	
********-0000-0000-0000-************	d720f8a1-53ad-4db7-a273-699abde6c014	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 03:36:19.158771+00	
********-0000-0000-0000-************	50df4dda-a652-419e-95d6-82b5d1c4e9a7	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 03:36:19.16565+00	
********-0000-0000-0000-************	8d53a775-5d93-463e-8a6b-c366c19bfecb	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 04:24:04.628971+00	
********-0000-0000-0000-************	7cfd4607-d4a2-4624-a3eb-a228e6a034e4	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 04:24:04.632368+00	
********-0000-0000-0000-************	efb06b78-5fbc-4dc8-907e-eb1cf69a9359	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 04:35:08.482223+00	
********-0000-0000-0000-************	df1dfe77-d28a-4644-ac46-dfb7431f8fae	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 04:35:08.484139+00	
********-0000-0000-0000-************	42a18973-86de-4128-a8d4-2da3c5e471e3	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 06:15:13.185069+00	
********-0000-0000-0000-************	ac37203d-30f1-41eb-97f8-1e9b70d71b3d	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 06:15:13.194648+00	
********-0000-0000-0000-************	d9333ff6-77f3-4010-b387-196023bee3c1	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 06:56:03.246657+00	
********-0000-0000-0000-************	7e911aac-2261-4855-9264-41a243cfec17	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 06:56:03.249047+00	
********-0000-0000-0000-************	6e90acdf-87a1-496b-a4e0-8dd5ce9fd9b1	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 07:17:46.45108+00	
********-0000-0000-0000-************	31e0772c-af3b-45d8-b79e-8df1b9c13fe3	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 07:17:46.451959+00	
********-0000-0000-0000-************	3a76912d-f436-4755-89e5-d6f3f55895c4	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 07:55:25.302646+00	
********-0000-0000-0000-************	29209543-8ed5-4663-b58c-55db00d92905	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 07:55:25.305005+00	
********-0000-0000-0000-************	709ef3ee-7c84-4539-874b-082f605fb031	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 04:11:57.252701+00	
********-0000-0000-0000-************	e346f457-89d8-4464-a9aa-25bd829bdcc1	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 04:11:57.267548+00	
********-0000-0000-0000-************	f2a671ad-71a1-40b1-97ec-fa9f04762f2b	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 11:42:32.147295+00	
********-0000-0000-0000-************	a793b5de-09c0-4ed9-83d4-c8ffc7168d25	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 11:42:32.159346+00	
********-0000-0000-0000-************	4d8592d7-04b7-4f2a-a43c-ac454c373844	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 11:42:42.719847+00	
********-0000-0000-0000-************	26a1b18a-32b7-4d03-90ee-59481593cfeb	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 11:42:42.720457+00	
********-0000-0000-0000-************	2545ee99-bc32-4d4f-96b9-96abf9909855	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 12:41:14.400879+00	
********-0000-0000-0000-************	b3ad6275-2cc0-4e77-b49e-3d10d2a0bb6c	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 12:41:14.405107+00	
********-0000-0000-0000-************	c61772d1-c019-43ed-9c62-d90ca63d4a53	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 13:39:29.232386+00	
********-0000-0000-0000-************	ff349122-6f77-47cf-a935-c9937327cc0a	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 13:39:29.235029+00	
********-0000-0000-0000-************	c76b20d7-410b-480a-8b75-b779fa71a078	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 14:43:02.585406+00	
********-0000-0000-0000-************	2e7f1661-1178-4137-ae1c-c305d351f708	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 14:43:02.591927+00	
********-0000-0000-0000-************	c2546e7e-aa1a-4598-8f06-fe6934e5c673	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 18:55:12.082407+00	
********-0000-0000-0000-************	e9a30e63-65b4-4fea-b1b6-0cfb1f9d90f4	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 18:55:12.09534+00	
********-0000-0000-0000-************	34f9d08a-fb18-4e68-befe-7a7d6a09c69d	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 06:34:31.190906+00	
********-0000-0000-0000-************	65238b84-fdd8-401d-b0fa-bed592075a26	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 06:34:31.206639+00	
********-0000-0000-0000-************	0962e1d0-4453-483f-950e-e2f73f1fc374	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 07:34:30.696853+00	
********-0000-0000-0000-************	2882ed5c-4fb3-417b-870f-2c501daedcaf	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 07:34:30.705172+00	
********-0000-0000-0000-************	322c1d63-19a4-4d1b-afa2-6787f97401f4	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-30 07:41:49.3608+00	
********-0000-0000-0000-************	3c9dcc82-ec68-4ab7-8c8c-a91f110e5397	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 08:33:08.445235+00	
********-0000-0000-0000-************	e9f1a020-1985-4897-a6bc-4e16bb3ca368	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 08:33:08.448034+00	
********-0000-0000-0000-************	a0897947-d93e-40ed-b40c-45d02b7fd0e6	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 09:40:42.513023+00	
********-0000-0000-0000-************	3b2b658a-77ee-4ff4-b7e1-978b9344a9a6	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 09:40:42.528981+00	
********-0000-0000-0000-************	f7c9a01a-ce22-40ee-867d-d6d74a0a3b4d	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 09:40:48.264932+00	
********-0000-0000-0000-************	44c567f9-9d41-4800-aa0c-d96334e6ec0a	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 09:40:48.265639+00	
********-0000-0000-0000-************	9d067f20-cd3c-4a2a-8d3c-f2db0d66bebc	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 10:39:27.738067+00	
********-0000-0000-0000-************	dd4e20c6-7759-4492-858e-90793aff20f1	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 10:39:27.742972+00	
********-0000-0000-0000-************	6f330ca4-6562-4eeb-87b5-4f61db2278ae	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 10:54:12.786425+00	
********-0000-0000-0000-************	97dde85b-2f88-40f6-983c-16d7b242c1de	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 10:54:12.788167+00	
********-0000-0000-0000-************	2f08a2e1-52fe-4435-9ddb-b9aa1979e30c	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 12:10:52.394375+00	
********-0000-0000-0000-************	dc37010d-0806-4960-87a4-8b915a947c49	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 12:10:52.39969+00	
********-0000-0000-0000-************	d7476152-72f6-4d04-8545-cd1a1b86105c	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 15:22:48.833639+00	
********-0000-0000-0000-************	5f7db344-0a0e-4d11-b039-3c6a39367338	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 15:22:48.844224+00	
********-0000-0000-0000-************	5832932d-1a99-421b-95cd-9cf9b164ce11	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 15:40:36.014792+00	
********-0000-0000-0000-************	d1c9ac07-131c-41c6-99b2-ae72b36adfdd	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 15:40:36.017072+00	
********-0000-0000-0000-************	415f3041-6f97-4d83-96bf-5559a9bc1353	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 16:21:28.405756+00	
********-0000-0000-0000-************	0c066fb2-630a-48c5-83c3-94abcbdfbe5d	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 16:21:28.408533+00	
********-0000-0000-0000-************	eea4440e-e993-4490-b7ce-29dfa5fdfaeb	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 16:43:58.965176+00	
********-0000-0000-0000-************	e4778752-1e54-4e21-919c-87bb8e054cc7	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 16:43:58.968706+00	
********-0000-0000-0000-************	61db99a5-9b76-4631-93e2-ffd3f2fc0662	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 17:36:36.807543+00	
********-0000-0000-0000-************	526d27ce-3fd8-43e1-93af-df0c4e56896b	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 17:36:36.810841+00	
********-0000-0000-0000-************	a6e94e99-ad9d-4def-b9d2-f7150e939a7c	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 18:21:12.904441+00	
********-0000-0000-0000-************	a9d0a984-22c9-4990-834b-d75cc5a85c5e	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 18:21:12.907125+00	
********-0000-0000-0000-************	a8d0b7cd-3ce1-4b78-95dd-158866e74e75	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 09:22:43.329238+00	
********-0000-0000-0000-************	e2f513b7-b3dd-4dcd-ae3b-5f58300f3b5e	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 09:22:43.346142+00	
********-0000-0000-0000-************	12328380-3323-42f6-8317-9b7513aaf397	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 12:40:56.27339+00	
********-0000-0000-0000-************	ba1e5894-ac22-41bb-985d-ec9124b3a7d4	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 12:40:56.291265+00	
********-0000-0000-0000-************	eb3573d8-e0dc-4f86-ac47-946f19683634	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-31 13:24:22.033551+00	
********-0000-0000-0000-************	42e957df-fc3d-4504-8507-af6418064c3f	{"action":"login","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-31 13:24:23.95889+00	
********-0000-0000-0000-************	402d20f3-e98a-4b96-a52c-39174020eeee	{"action":"logout","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-31 13:30:51.231011+00	
********-0000-0000-0000-************	3c00b597-6536-4e50-8174-8e726b2ffd15	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-31 13:30:55.502867+00	
********-0000-0000-0000-************	e78687a6-607c-4ce4-b57b-404a09914926	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 14:57:52.451588+00	
********-0000-0000-0000-************	efa9ead3-eb21-45d5-a6d6-d6f2d2d7b6d9	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 14:57:52.454761+00	
********-0000-0000-0000-************	1a1172a5-dddd-4a7f-85a6-8119d9b205d7	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-31 15:15:23.738902+00	
********-0000-0000-0000-************	5e3f5167-7d89-4c0c-96fb-8c1a6cd5f83d	{"action":"login","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-31 15:15:38.710367+00	
********-0000-0000-0000-************	baeb32d2-7b6f-4b27-8b37-ec5a78f6f11a	{"action":"logout","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-31 15:16:16.870045+00	
********-0000-0000-0000-************	3b2ba0f2-b1d8-4068-8312-7b33f0f33b08	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-31 15:16:20.311118+00	
********-0000-0000-0000-************	17dab7f3-dd4d-4209-a390-a5c3d7be2876	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-31 15:22:08.959641+00	
********-0000-0000-0000-************	3e3016dc-92f6-4e27-b5d1-427ed8581a06	{"action":"login","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-31 15:22:18.575229+00	
********-0000-0000-0000-************	fa460dac-a7ef-4a95-8807-5b8b8a87d5c1	{"action":"logout","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-31 15:31:49.520293+00	
********-0000-0000-0000-************	8aff75a4-6251-4018-a3c5-cc8ada28ee39	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-31 15:31:52.595939+00	
********-0000-0000-0000-************	399c6d39-a3d6-4821-b1e0-18a909ccfbd2	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 16:30:22.277001+00	
********-0000-0000-0000-************	16fcf269-7715-45cd-b423-26bc7c6a88c6	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 16:30:22.286461+00	
********-0000-0000-0000-************	ad3d3a5c-628f-480e-b50c-6ddec6a47f9e	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-31 17:12:14.047105+00	
********-0000-0000-0000-************	7e85b654-a465-4208-b58d-e2a89c7c187c	{"action":"login","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-31 17:12:18.826608+00	
********-0000-0000-0000-************	182fe021-aa1e-4562-81dc-06b95a4ac953	{"action":"token_refreshed","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 18:12:11.909782+00	
********-0000-0000-0000-************	c5bc5999-9210-46dc-a533-6b00bf250985	{"action":"token_revoked","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 18:12:11.916745+00	
********-0000-0000-0000-************	be47471c-07b7-494a-bbad-c8c712217e07	{"action":"token_refreshed","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 19:55:35.055875+00	
********-0000-0000-0000-************	ad6969c0-f791-4249-ae56-24e36ddbbc8b	{"action":"token_revoked","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 19:55:35.059543+00	
********-0000-0000-0000-************	88cd37bc-a1de-4179-a078-f76543ea7231	{"action":"logout","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-31 19:56:08.987802+00	
********-0000-0000-0000-************	36b06188-e377-4465-8b94-3f6dfa8e8a23	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-31 19:56:13.52577+00	
********-0000-0000-0000-************	3bb645c4-5a33-4938-b8b4-015324c254b5	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 21:15:38.266274+00	
********-0000-0000-0000-************	e14eaaf3-8ba6-48e3-a762-eb57dc44dd14	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 21:15:38.271679+00	
********-0000-0000-0000-************	6c16f5ae-0a77-4e02-abf9-a741ab44d064	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 06:16:55.273691+00	
********-0000-0000-0000-************	002ef688-8aac-4759-af64-d7c0c0e00f5a	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 06:16:55.294476+00	
********-0000-0000-0000-************	c0125df8-7a5c-48d9-bd8f-34722ff38d8e	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-08-01 06:44:38.540728+00	
********-0000-0000-0000-************	7f1a8a12-2408-48a1-a586-6c190557eeec	{"action":"login","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-08-01 06:44:53.197106+00	
********-0000-0000-0000-************	1fbdbc3d-85cc-4ece-93c2-471d50193191	{"action":"logout","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-08-01 06:47:46.759619+00	
********-0000-0000-0000-************	714567fa-0e1a-48f8-8330-cdea8e0a0fc2	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-08-01 06:47:51.982151+00	
********-0000-0000-0000-************	0c144628-b6de-419f-9ae5-eb4c72afbf1b	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 07:46:14.476414+00	
********-0000-0000-0000-************	a58937ce-20fb-416a-ad72-8afb8e3998d2	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 07:46:14.482475+00	
********-0000-0000-0000-************	97b146b7-c858-4e16-bcf6-6fb523256607	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 08:47:15.859443+00	
********-0000-0000-0000-************	eaaffcb9-7a9d-47c1-8937-c50bec6bfe06	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 08:47:15.866392+00	
********-0000-0000-0000-************	5a4d87c2-3b57-46b9-b742-7ec9b3c3f8a1	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-08-01 14:08:02.684584+00	
********-0000-0000-0000-************	29706e0d-cbaf-43cf-b1a6-76d59340969e	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 15:15:36.3165+00	
********-0000-0000-0000-************	6de2d4d0-3731-42f3-9b50-649ad898fca2	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 15:15:36.319823+00	
********-0000-0000-0000-************	7f29dd4e-d8e9-4dbe-b0d0-acf22cf4b9d3	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 16:48:12.260074+00	
********-0000-0000-0000-************	dcd8897b-6d5f-4536-b63f-02615fd937ee	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 16:48:12.262496+00	
********-0000-0000-0000-************	b62de546-4b00-4c15-8dc9-1b7766263803	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-08-01 16:49:07.518692+00	
********-0000-0000-0000-************	e09828e5-e045-4948-bfe9-29eff2edd23d	{"action":"login","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-08-01 16:49:17.513601+00	
********-0000-0000-0000-************	9399c62a-4b47-4398-b06c-918d90b8299d	{"action":"token_refreshed","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 17:48:57.847613+00	
********-0000-0000-0000-************	950800cd-8313-4649-837a-0587dee19c35	{"action":"token_revoked","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 17:48:57.850092+00	
********-0000-0000-0000-************	43d21ea4-78ef-47c1-af03-5a0a0adb51e4	{"action":"token_refreshed","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 20:54:15.10846+00	
********-0000-0000-0000-************	318302a9-6fd7-4599-befe-38f7d7e8ca5a	{"action":"token_revoked","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 20:54:15.121589+00	
********-0000-0000-0000-************	850df701-29eb-4f4c-a444-a00429f89402	{"action":"logout","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-08-01 20:55:14.390234+00	
********-0000-0000-0000-************	0197c733-9347-48bd-a80d-8e1225a082b7	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-08-01 20:55:21.062985+00	
********-0000-0000-0000-************	9c423438-b301-4638-97b9-b40460a2c957	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"a364b8e7-dbca-4b58-a3e6-ad2f24684931","user_phone":""}}	2025-08-02 06:07:03.960483+00	
********-0000-0000-0000-************	7446ac8f-f0ec-4d4f-9bb5-b38734b6efe0	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 06:08:58.768166+00	
********-0000-0000-0000-************	7b5ba855-7b77-4afe-837b-a1e8f068a467	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 06:08:58.769753+00	
********-0000-0000-0000-************	6944e54b-9dca-4799-9d99-a688b23370eb	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-08-02 06:15:04.404808+00	
********-0000-0000-0000-************	7b30ce2d-b833-4259-b78d-f0a633163b2c	{"action":"user_confirmation_requested","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-08-02 06:15:45.560541+00	
********-0000-0000-0000-************	a1d17903-d723-4ddd-8b9e-73d5ce21db16	{"action":"user_signedup","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"team"}	2025-08-02 06:16:52.239163+00	
********-0000-0000-0000-************	58d237e6-b138-4942-9f1c-2cf695715855	{"action":"token_refreshed","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 07:33:21.51283+00	
********-0000-0000-0000-************	85a01b3c-fc82-4d42-bd67-2ba485c715bc	{"action":"token_revoked","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 07:33:21.519105+00	
********-0000-0000-0000-************	0b7f2ae1-0d0c-4189-9de2-cb28a3895f77	{"action":"logout","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-08-02 07:44:44.81839+00	
********-0000-0000-0000-************	b9f5eeba-3ad5-4012-9c03-79b280107e95	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-08-02 07:44:46.760871+00	
********-0000-0000-0000-************	6334906d-7218-4172-8908-037ad84549a3	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 09:47:26.421863+00	
********-0000-0000-0000-************	a1957482-9b53-458e-bf33-c8c47b10c54c	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 09:47:26.444133+00	
********-0000-0000-0000-************	4384c918-aa07-4fc3-8162-81484d6f8dbd	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-08-02 09:49:28.105397+00	
********-0000-0000-0000-************	b1def1a8-1525-41d3-9abf-ce8fb1225454	{"action":"login","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-08-02 09:49:34.351409+00	
********-0000-0000-0000-************	0e0afcab-8612-48c4-a825-bd5aef049ffd	{"action":"logout","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-08-02 09:50:27.32277+00	
********-0000-0000-0000-************	eab6433e-a8d0-404f-aca7-14110c57d1ae	{"action":"login","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-08-02 09:50:35.581592+00	
********-0000-0000-0000-************	6d1dee31-4d9c-4676-b274-f45fa04645ce	{"action":"login","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-08-02 09:57:19.62737+00	
********-0000-0000-0000-************	a0a5fd10-6815-4367-a833-080ad7e16aa1	{"action":"token_refreshed","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 10:57:31.964814+00	
********-0000-0000-0000-************	3185de67-189d-4a26-8c64-a92ad680182f	{"action":"token_revoked","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 10:57:31.968391+00	
********-0000-0000-0000-************	881139d3-b8ef-47af-be04-19aa5701d7e2	{"action":"token_refreshed","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 12:20:20.893529+00	
********-0000-0000-0000-************	5d1ebccc-be62-427c-a449-c7466c3ee920	{"action":"token_revoked","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 12:20:20.903121+00	
********-0000-0000-0000-************	518197fd-ce1c-4208-bb9b-a6fb4dbc88ae	{"action":"token_refreshed","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 14:53:35.364498+00	
********-0000-0000-0000-************	9cecb903-b1ad-43ac-82b7-ad5051291cfb	{"action":"token_revoked","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 14:53:35.381158+00	
********-0000-0000-0000-************	494b1e5d-95bd-44cc-98ec-6ff0fb49dbcd	{"action":"token_refreshed","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 16:46:23.026252+00	
********-0000-0000-0000-************	9421e349-6a8d-4e17-bb83-4d2d3da8cc43	{"action":"token_revoked","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 16:46:23.034732+00	
********-0000-0000-0000-************	cb9beb14-2436-47a6-8cb8-d801f9b3b068	{"action":"token_refreshed","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 16:48:06.401776+00	
********-0000-0000-0000-************	ad8c9b82-b87d-4ae5-a4ec-ebd7f98f424c	{"action":"token_revoked","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 16:48:06.404353+00	
********-0000-0000-0000-************	c3faff26-6f64-4cb9-81dc-2c720115273d	{"action":"token_refreshed","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 17:46:08.472812+00	
********-0000-0000-0000-************	4042ea51-96d3-45c4-b2eb-0fe79bcb2fd4	{"action":"token_revoked","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 17:46:08.490566+00	
********-0000-0000-0000-************	d91e1145-8041-4f1b-9584-90f2d82ebd7d	{"action":"token_refreshed","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 17:50:26.284185+00	
********-0000-0000-0000-************	39ee05ea-8856-407c-86c2-52a79963246f	{"action":"token_revoked","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 17:50:26.287874+00	
********-0000-0000-0000-************	82ce193c-c93a-46dd-b6d3-058b9675c16d	{"action":"token_refreshed","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 18:44:55.76225+00	
********-0000-0000-0000-************	a5c209d1-9993-4605-8cbd-4cb88452d03a	{"action":"token_revoked","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 18:44:55.764609+00	
********-0000-0000-0000-************	8d7ded1a-37d7-4c7d-be68-af0fae8d39c2	{"action":"token_refreshed","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 20:00:14.434014+00	
********-0000-0000-0000-************	24d845b1-0a5e-4ff2-9c1a-dffc31c56ccb	{"action":"token_revoked","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 20:00:14.444126+00	
********-0000-0000-0000-************	a1772329-ec09-4942-9ccd-dcf032c94a2a	{"action":"token_refreshed","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 21:02:12.049228+00	
********-0000-0000-0000-************	eaee1835-10dc-42db-9f0c-27980111ed0a	{"action":"token_revoked","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 21:02:12.058544+00	
********-0000-0000-0000-************	fdb318ce-add5-4b3a-ad16-577327fe863f	{"action":"login","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-08-03 05:43:56.511324+00	
********-0000-0000-0000-************	a42d5fe2-d0f6-46c8-a87b-f4f2d51d66c2	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-08-03 06:00:10.580859+00	
\.


--
-- Data for Name: flow_state; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.flow_state (id, user_id, auth_code, code_challenge_method, code_challenge, provider_type, provider_access_token, provider_refresh_token, created_at, updated_at, authentication_method, auth_code_issued_at) FROM stdin;
0a2cd87c-42ae-4baf-8b03-1231ef45e44e	cac9897f-3814-4a5c-8890-79b2e35c52a8	bf4a597b-eef2-42f8-9514-9e2725cdfc01	s256	KegwoNJ5pa74iw04oLD4wJdGD3_BmSiNnCBso9cx8Bo	email			2025-07-19 04:14:54.992816+00	2025-07-19 04:14:54.992816+00	email/signup	\N
61e07099-b607-498f-ace1-c923bf765827	6e029114-7c26-4dd7-a5e8-4cfe0f16ffaf	9231768f-c524-438d-bf30-2687b3bdbd77	s256	1iJ2kWq5Ocdm-Ai3C-2yAiUvc2JQQzvhuv8vG8cE8rQ	email			2025-07-19 06:08:35.015682+00	2025-07-19 06:08:35.015682+00	email/signup	\N
2aa9c311-0990-4f1e-87d1-fc8361112d7e	27ba2b34-328a-4e06-919a-3f97cb46fd30	5efb701a-25db-4074-8713-221184d6775b	s256	j3vHJwKtrXTjncaq2Z6p-ObdOaCJcdKEiPT0LBbfqVE	email			2025-07-19 06:11:52.101959+00	2025-07-19 06:11:52.101959+00	email/signup	\N
daba3f99-6f6c-40f2-a05f-e24a12ce262d	ea6c9640-8770-4ea8-ae82-5e2a178245d5	be47d397-ce68-4e43-ab92-3acad33dae40	s256	ukUDXTSlNW9vROctCf9vAxyfIGiai7cfXhMAC6lLqcU	email			2025-07-19 06:16:02.14635+00	2025-07-19 06:16:02.14635+00	email/signup	\N
62de1fd9-3d03-48ec-862d-6f1e1188a2b9	ea6c9640-8770-4ea8-ae82-5e2a178245d5	3f8d8fc7-ed65-4189-828f-2b741c949aa2	s256	NoT-KWDP3QiZ6vqSsZEfuAJT_EUsVvvIa-r_NBkDqUM	email			2025-07-19 06:17:47.298782+00	2025-07-19 06:17:47.298782+00	email/signup	\N
47263ecb-cf7a-4120-b995-53f930a9582f	5dbe8b46-856d-48d8-ba4b-086ac2bcea4a	19e30b85-6f49-4f00-a00a-d35df66c0c39	s256	_899SG_JuGUkw3RTHrFeNHKj3RTJ3XUWTJaCS7S9XDI	email			2025-07-19 06:32:05.377225+00	2025-07-19 06:32:05.377225+00	email/signup	\N
6e205194-455f-4961-85a7-6d7260804919	a364b8e7-dbca-4b58-a3e6-ad2f24684931	26637321-d805-4ba6-b62c-ca85598d3e26	s256	-gM403eouqZtE0Z8l1DBmJHHhbnh_im_5YuBL11RfQA	email			2025-07-19 06:35:11.94603+00	2025-07-19 06:35:11.94603+00	email/signup	\N
235637f8-51c8-414c-9ddc-876d23beb6fa	a364b8e7-dbca-4b58-a3e6-ad2f24684931	5796133e-faff-438f-9a9d-e2bf5ffc6440	s256	zBv8wQoUYSHA9Vjql8qo47A90dGl5DN0GlpT5tFKlxY	email			2025-07-19 06:38:12.662442+00	2025-07-19 06:38:12.662442+00	email/signup	\N
8bc2289b-3b43-48f2-b2bf-6b23ad8173a5	a364b8e7-dbca-4b58-a3e6-ad2f24684931	0c668d41-e1e9-437f-8dab-b942eca5337d	s256	zFJERPBN4aQC9ckI00JWohhdR6ZCBbxCvKL2YVuPZ_A	email			2025-07-19 06:39:56.302491+00	2025-07-19 06:39:56.302491+00	email/signup	\N
39a07f9b-302a-4e4d-9bfb-17fd98ea48de	fa835c38-a6b7-483b-b030-3bbf279f6a1b	7aa6b565-e858-4f86-986c-034f02d535e4	s256	yrEXhJwKHjmleh5NWUBAJEODurh1EngfwXytL-LHc0s	email			2025-07-19 06:41:32.546622+00	2025-07-19 06:41:32.546622+00	email/signup	\N
b1510c20-4039-438b-b76f-6eb33f1bbc22	fa835c38-a6b7-483b-b030-3bbf279f6a1b	e4fc5706-cf37-4557-bd7b-741f12ee6f20	s256	eeq5KlV5L-NPTusC97BoMdonoDodDnmaBcGjtF4TYv8	email			2025-07-19 06:42:34.561532+00	2025-07-19 06:42:34.561532+00	email/signup	\N
0000f3be-96e0-4335-aa89-e24357147c26	fa322dd1-4dad-42a3-b68e-30407d5629b7	f788af1d-0354-4a34-862f-f0ac77f1817e	s256	8iE04y2qQb_ozRQ_X94NpibEPR39h-VETp6-CggWZl4	email			2025-08-02 06:15:45.561245+00	2025-08-02 06:15:45.561245+00	email/signup	\N
\.


--
-- Data for Name: identities; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.identities (provider_id, user_id, identity_data, provider, last_sign_in_at, created_at, updated_at, id) FROM stdin;
fa835c38-a6b7-483b-b030-3bbf279f6a1b	fa835c38-a6b7-483b-b030-3bbf279f6a1b	{"sub": "fa835c38-a6b7-483b-b030-3bbf279f6a1b", "name": "邮箱测试", "email": "<EMAIL>", "display_name": "邮箱测试", "email_verified": true, "phone_verified": false}	email	2025-07-19 06:41:32.5421+00	2025-07-19 06:41:32.542157+00	2025-07-19 06:41:32.542157+00	1ec5bc97-7d38-4fe4-9592-f8d9a9df3326
e432fabd-c944-4c01-9dcd-2e687287c8dc	e432fabd-c944-4c01-9dcd-2e687287c8dc	{"sub": "e432fabd-c944-4c01-9dcd-2e687287c8dc", "name": "Haoyv Bin", "email": "<EMAIL>", "display_name": "Haoyv Bin", "email_verified": true, "phone_verified": false}	email	2025-06-17 16:22:20.130638+00	2025-06-17 16:22:20.130727+00	2025-06-17 16:22:20.130727+00	e9975270-09e6-439f-814b-ba3d8d4110fc
fa322dd1-4dad-42a3-b68e-30407d5629b7	fa322dd1-4dad-42a3-b68e-30407d5629b7	{"sub": "fa322dd1-4dad-42a3-b68e-30407d5629b7", "name": "HaoyvBin", "email": "<EMAIL>", "display_name": "HaoyvBin", "email_verified": true, "phone_verified": false}	email	2025-08-02 06:15:45.54929+00	2025-08-02 06:15:45.549323+00	2025-08-02 06:15:45.549323+00	acf42507-da61-4032-87e9-c7cc900eae1f
\.


--
-- Data for Name: instances; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.instances (id, uuid, raw_base_config, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: mfa_amr_claims; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.mfa_amr_claims (session_id, created_at, updated_at, authentication_method, id) FROM stdin;
6912ded7-acda-435f-9782-360a89d86e47	2025-08-02 09:50:35.584271+00	2025-08-02 09:50:35.584271+00	password	a096e8cb-4355-468a-a329-bc74874a7c67
0bb62aba-33b4-4af6-bc34-10ec2ebe7334	2025-08-02 09:57:19.629877+00	2025-08-02 09:57:19.629877+00	password	eb4bc73c-57a4-44f2-b664-b481ed7863be
9ae8dcf1-233e-4be4-813c-f5d6aea417f6	2025-08-03 05:43:56.515781+00	2025-08-03 05:43:56.515781+00	password	864cd32f-5dbf-415c-9661-e2d0d480e1e7
f2b77435-018f-476d-bca2-fce3c8a978cd	2025-08-03 06:00:10.588784+00	2025-08-03 06:00:10.588784+00	password	b085bfd8-ef7c-4e50-97ba-0209a8b86295
\.


--
-- Data for Name: mfa_challenges; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.mfa_challenges (id, factor_id, created_at, verified_at, ip_address, otp_code, web_authn_session_data) FROM stdin;
\.


--
-- Data for Name: mfa_factors; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.mfa_factors (id, user_id, friendly_name, factor_type, status, created_at, updated_at, secret, phone, last_challenged_at, web_authn_credential, web_authn_aaguid) FROM stdin;
\.


--
-- Data for Name: one_time_tokens; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.one_time_tokens (id, user_id, token_type, token_hash, relates_to, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: refresh_tokens; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.refresh_tokens (instance_id, id, token, user_id, revoked, created_at, updated_at, parent, session_id) FROM stdin;
********-0000-0000-0000-************	323	npdmx2sqoz4k	fa322dd1-4dad-42a3-b68e-30407d5629b7	t	2025-08-02 09:57:19.628775+00	2025-08-02 10:57:31.972482+00	\N	0bb62aba-33b4-4af6-bc34-10ec2ebe7334
********-0000-0000-0000-************	324	e7erdjllidhd	fa322dd1-4dad-42a3-b68e-30407d5629b7	t	2025-08-02 10:57:31.974937+00	2025-08-02 12:20:20.904124+00	npdmx2sqoz4k	0bb62aba-33b4-4af6-bc34-10ec2ebe7334
********-0000-0000-0000-************	325	nlgvvayifjmg	fa322dd1-4dad-42a3-b68e-30407d5629b7	t	2025-08-02 12:20:20.910238+00	2025-08-02 14:53:35.382192+00	e7erdjllidhd	0bb62aba-33b4-4af6-bc34-10ec2ebe7334
********-0000-0000-0000-************	326	ggziv4hk6sqs	fa322dd1-4dad-42a3-b68e-30407d5629b7	t	2025-08-02 14:53:35.38908+00	2025-08-02 16:46:23.035787+00	nlgvvayifjmg	0bb62aba-33b4-4af6-bc34-10ec2ebe7334
********-0000-0000-0000-************	322	sqdw6ligc2pe	fa322dd1-4dad-42a3-b68e-30407d5629b7	t	2025-08-02 09:50:35.583368+00	2025-08-02 16:48:06.405309+00	\N	6912ded7-acda-435f-9782-360a89d86e47
********-0000-0000-0000-************	327	awurhcqfg3yx	fa322dd1-4dad-42a3-b68e-30407d5629b7	t	2025-08-02 16:46:23.045182+00	2025-08-02 17:46:08.491829+00	ggziv4hk6sqs	0bb62aba-33b4-4af6-bc34-10ec2ebe7334
********-0000-0000-0000-************	328	37zkwsy22rtc	fa322dd1-4dad-42a3-b68e-30407d5629b7	t	2025-08-02 16:48:06.406435+00	2025-08-02 17:50:26.288462+00	sqdw6ligc2pe	6912ded7-acda-435f-9782-360a89d86e47
********-0000-0000-0000-************	329	oemd56or2cs4	fa322dd1-4dad-42a3-b68e-30407d5629b7	t	2025-08-02 17:46:08.500892+00	2025-08-02 18:44:55.765864+00	awurhcqfg3yx	0bb62aba-33b4-4af6-bc34-10ec2ebe7334
********-0000-0000-0000-************	331	oowtm325hw3o	fa322dd1-4dad-42a3-b68e-30407d5629b7	f	2025-08-02 18:44:55.767834+00	2025-08-02 18:44:55.767834+00	oemd56or2cs4	0bb62aba-33b4-4af6-bc34-10ec2ebe7334
********-0000-0000-0000-************	330	qjfxkcmt322d	fa322dd1-4dad-42a3-b68e-30407d5629b7	t	2025-08-02 17:50:26.289261+00	2025-08-02 20:00:14.4456+00	37zkwsy22rtc	6912ded7-acda-435f-9782-360a89d86e47
********-0000-0000-0000-************	332	gwwqkzxdrrif	fa322dd1-4dad-42a3-b68e-30407d5629b7	t	2025-08-02 20:00:14.451609+00	2025-08-02 21:02:12.05918+00	qjfxkcmt322d	6912ded7-acda-435f-9782-360a89d86e47
********-0000-0000-0000-************	333	z6p7xjfdbisb	fa322dd1-4dad-42a3-b68e-30407d5629b7	f	2025-08-02 21:02:12.064273+00	2025-08-02 21:02:12.064273+00	gwwqkzxdrrif	6912ded7-acda-435f-9782-360a89d86e47
********-0000-0000-0000-************	334	ef7j56a2fgu2	fa322dd1-4dad-42a3-b68e-30407d5629b7	f	2025-08-03 05:43:56.514021+00	2025-08-03 05:43:56.514021+00	\N	9ae8dcf1-233e-4be4-813c-f5d6aea417f6
********-0000-0000-0000-************	335	gknwibbtmdb6	e432fabd-c944-4c01-9dcd-2e687287c8dc	f	2025-08-03 06:00:10.585454+00	2025-08-03 06:00:10.585454+00	\N	f2b77435-018f-476d-bca2-fce3c8a978cd
\.


--
-- Data for Name: saml_providers; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.saml_providers (id, sso_provider_id, entity_id, metadata_xml, metadata_url, attribute_mapping, created_at, updated_at, name_id_format) FROM stdin;
\.


--
-- Data for Name: saml_relay_states; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.saml_relay_states (id, sso_provider_id, request_id, for_email, redirect_to, created_at, updated_at, flow_state_id) FROM stdin;
\.


--
-- Data for Name: schema_migrations; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.schema_migrations (version) FROM stdin;
20171026211738
20171026211808
20171026211834
20180103212743
20180108183307
20180119214651
20180125194653
00
20210710035447
20210722035447
20210730183235
20210909172000
20210927181326
20211122151130
20211124214934
20211202183645
20220114185221
20220114185340
20220224000811
20220323170000
20220429102000
20220531120530
20220614074223
20220811173540
20221003041349
20221003041400
20221011041400
20221020193600
20221021073300
20221021082433
20221027105023
20221114143122
20221114143410
20221125140132
20221208132122
20221215195500
20221215195800
20221215195900
20230116124310
20230116124412
20230131181311
20230322519590
20230402418590
20230411005111
20230508135423
20230523124323
20230818113222
20230914180801
20231027141322
20231114161723
20231117164230
20240115144230
20240214120130
20240306115329
20240314092811
20240427152123
20240612123726
20240729123726
20240802193726
20240806073726
20241009103726
\.


--
-- Data for Name: sessions; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.sessions (id, user_id, created_at, updated_at, factor_id, aal, not_after, refreshed_at, user_agent, ip, tag) FROM stdin;
0bb62aba-33b4-4af6-bc34-10ec2ebe7334	fa322dd1-4dad-42a3-b68e-30407d5629b7	2025-08-02 09:57:19.628133+00	2025-08-02 18:44:55.771758+00	\N	aal1	\N	2025-08-02 18:44:55.771717	Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********	**********	\N
6912ded7-acda-435f-9782-360a89d86e47	fa322dd1-4dad-42a3-b68e-30407d5629b7	2025-08-02 09:50:35.582743+00	2025-08-02 21:02:12.073623+00	\N	aal1	\N	2025-08-02 21:02:12.073584	Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36	**********	\N
9ae8dcf1-233e-4be4-813c-f5d6aea417f6	fa322dd1-4dad-42a3-b68e-30407d5629b7	2025-08-03 05:43:56.512681+00	2025-08-03 05:43:56.512681+00	\N	aal1	\N	\N	Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36	************	\N
f2b77435-018f-476d-bca2-fce3c8a978cd	e432fabd-c944-4c01-9dcd-2e687287c8dc	2025-08-03 06:00:10.582893+00	2025-08-03 06:00:10.582893+00	\N	aal1	\N	\N	Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********	58.59.187.11	\N
\.


--
-- Data for Name: sso_domains; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.sso_domains (id, sso_provider_id, domain, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: sso_providers; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.sso_providers (id, resource_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.users (instance_id, id, aud, role, email, encrypted_password, email_confirmed_at, invited_at, confirmation_token, confirmation_sent_at, recovery_token, recovery_sent_at, email_change_token_new, email_change, email_change_sent_at, last_sign_in_at, raw_app_meta_data, raw_user_meta_data, is_super_admin, created_at, updated_at, phone, phone_confirmed_at, phone_change, phone_change_token, phone_change_sent_at, email_change_token_current, email_change_confirm_status, banned_until, reauthentication_token, reauthentication_sent_at, is_sso_user, deleted_at, is_anonymous) FROM stdin;
********-0000-0000-0000-************	fa835c38-a6b7-483b-b030-3bbf279f6a1b	authenticated	authenticated	<EMAIL>	$2a$10$1Nrs3y2TPtSqsnKoC7CoOeonGoEfYOHHfZSuKWYGCFI8C59IMnIje	2025-07-19 06:43:57.903258+00	\N		2025-07-19 06:43:43.850212+00		\N			\N	2025-08-02 09:49:34.351918+00	{"provider": "email", "providers": ["email"]}	{"sub": "fa835c38-a6b7-483b-b030-3bbf279f6a1b", "name": "邮箱测试", "email": "<EMAIL>", "display_name": "邮箱测试", "email_verified": true, "phone_verified": false}	\N	2025-07-19 06:41:32.531631+00	2025-08-02 09:49:34.357782+00	\N	\N			\N		0	\N		\N	f	\N	f
********-0000-0000-0000-************	fa322dd1-4dad-42a3-b68e-30407d5629b7	authenticated	authenticated	<EMAIL>	$2a$10$hu1vwBjpkW4p220rW0Kw1.hVrTx.RPZ6XIyKKu.UN5ugP06ovLGwu	2025-08-02 06:16:52.239844+00	\N		2025-08-02 06:15:45.573235+00		\N			\N	2025-08-03 05:43:56.512617+00	{"provider": "email", "providers": ["email"]}	{"sub": "fa322dd1-4dad-42a3-b68e-30407d5629b7", "name": "HaoyvBin", "email": "<EMAIL>", "display_name": "HaoyvBin", "email_verified": true, "phone_verified": false}	\N	2025-08-02 06:15:45.521481+00	2025-08-03 05:43:56.515173+00	\N	\N			\N		0	\N		\N	f	\N	f
********-0000-0000-0000-************	e432fabd-c944-4c01-9dcd-2e687287c8dc	authenticated	authenticated	<EMAIL>	$2a$10$FhXRoa21Qwc0ksbqj04qGOzf.08EuY7erl0NC66p1bBuY3.h03S0G	2025-06-17 16:22:33.729747+00	\N		2025-06-17 16:22:20.143923+00		\N			\N	2025-08-03 06:00:10.582793+00	{"provider": "email", "providers": ["email"]}	{"sub": "e432fabd-c944-4c01-9dcd-2e687287c8dc", "name": "Haoyv Bin", "email": "<EMAIL>", "display_name": "Haoyv Bin", "email_verified": true, "phone_verified": false}	\N	2025-06-17 16:22:20.082149+00	2025-08-03 06:00:10.587914+00	\N	\N			\N		0	\N		\N	f	\N	f
\.


--
-- Data for Name: job; Type: TABLE DATA; Schema: cron; Owner: supabase_admin
--

COPY cron.job (jobid, schedule, command, nodename, nodeport, database, username, active, jobname) FROM stdin;
\.


--
-- Data for Name: job_run_details; Type: TABLE DATA; Schema: cron; Owner: supabase_admin
--

COPY cron.job_run_details (jobid, runid, job_pid, database, username, command, status, return_message, start_time, end_time) FROM stdin;
\.


--
-- Data for Name: membership-look; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."membership-look" (id, email, membership_level, word_count_limit, word_count_used, created_at, updated_at, user_id, is_verified, daily_free_quota, daily_free_used, last_free_reset_date, reward_quota, reward_used) FROM stdin;
66396a45-2114-46cc-afaa-df057a72490f	<EMAIL>	免费	0	0	2025-07-19 06:41:32.531029+00	2025-08-02 12:35:00.045746+00	fa835c38-a6b7-483b-b030-3bbf279f6a1b	t	25	0	2025-08-02	0	0
c78b34f1-d72b-4569-999a-2febf4280c3e	<EMAIL>	免费	0	0	2025-08-02 06:15:45.521247+00	2025-08-02 21:05:10.135821+00	fa322dd1-4dad-42a3-b68e-30407d5629b7	t	25	21	2025-08-02	0	0
ff14dfee-d997-415b-997c-c0c828a9dac9	<EMAIL>	黑金	1********	0	2025-06-17 16:22:20.081258+00	2025-08-03 06:00:30.614905+00	e432fabd-c944-4c01-9dcd-2e687287c8dc	t	25	1	2025-08-03	14	0
\.


--
-- Data for Name: membership-true; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."membership-true" (id, email, membership_level, word_count_limit, word_count_used, created_at, updated_at, user_id, is_verified, daily_free_quota, daily_free_used, last_free_reset_date, reward_quota, reward_used) FROM stdin;
66396a45-2114-46cc-afaa-df057a72490f	<EMAIL>	免费	0	0	2025-07-19 06:41:32.531029+00	2025-08-02 12:35:00.045746+00	fa835c38-a6b7-483b-b030-3bbf279f6a1b	t	25	0	2025-08-02	0	0
c78b34f1-d72b-4569-999a-2febf4280c3e	<EMAIL>	免费	0	0	2025-08-02 06:15:45.521247+00	2025-08-02 21:05:10.135821+00	fa322dd1-4dad-42a3-b68e-30407d5629b7	t	25	21	2025-08-02	0	0
ff14dfee-d997-415b-997c-c0c828a9dac9	<EMAIL>	黑金	1********	0	2025-06-17 16:22:20.081258+00	2025-08-03 06:00:30.614905+00	e432fabd-c944-4c01-9dcd-2e687287c8dc	t	25	1	2025-08-03	14	0
\.


--
-- Data for Name: novel_files; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.novel_files (id, user_id, file_name, file_path, file_size, mime_type, upload_time, minio_bucket, minio_object_key, created_at, work_title) FROM stdin;
8f7fba64-a4de-44be-8705-48e3af2aa359	fa322dd1-4dad-42a3-b68e-30407d5629b7	正文.txt	xiaoshuo/<EMAIL>/8f7fba64-a4de-44be-8705-48e3af2aa359/正文/诡舍抬头的人.txt	441794	text/plain; charset=utf-8	2025-08-02 16:48:47.271142+00	xiaoshuo	xiaoshuo/<EMAIL>/8f7fba64-a4de-44be-8705-48e3af2aa359/正文/诡舍抬头的人.txt	2025-08-02 16:48:47.271142+00	诡舍抬头的人
\.


--
-- Data for Name: propmt-neirong; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."propmt-neirong" (prompt_id, content, created_at, updated_at, title, author_display_id, usage_count) FROM stdin;
324b290f-c303-4395-b104-34f304c7bfa5	你是一名专业文学分析助手，擅长解构长篇小说结构、提炼核心元素并指导仿写创作，你的任务是拆解长篇小说大纲结构、提炼核心梗与叙事模式、生成仿写指导方案。你具备文本深度语义分析、故事模式归纳与跨作品迁移、风格要素量化比对等能力，拥有经典/流行小说叙事理论、番茄小说结构数据库、写作技法库（悬念设置/节奏控制等）、番茄，起点平台热门作品特征分析等知识储备。\n\n你的工作流程如下：\n1. 输入：长篇小说文本/完整故事梗概。\n2. 分析：\n    - 大纲结构（起承转合/关键事件节点）。\n    - 核心梗（主线冲突/创新设定/记忆点）。\n3. 输出：\n    - 结构模板（可替换要素标记）。\n\n输出格式：\n《作品名称》创作要素拆解\n大纲结构\n章节架构：[阶段名称]-[功能]-[关键事件]\n节奏图谱：[高潮间隔]/[铺垫比例]\n核心梗提炼\n主线公式：[类型]×[冲突]＋[特色元素]\n复用建议：[可替换组件]/[禁忌点]\n原章纲结构拆解\n阶段标记：[引入/发展/转折/高潮/收束]\n功能节点：[世界观铺设]/[人物关系建立]/[核心冲突触发]\n元素置换规则\n时空坐标：[现代都市→古代架空] 约束：剧情及要素必须贴合当前时空背景，逻辑自洽\n角色原型：[主角的母亲→主角的妹妹]\n冲突类型：[权力争夺→家庭伦理]\n置换后大纲输出\n触发事件：[旧元素]→[置换元素]\n推进逻辑：[保留原结构关系]\n悬念植入：[原悬念类型]→[适配新世界观]\n\n你的具体任务是为多个关联的章节分别拆出10条情节点细纲，这些情节点要能够总结对应章节的剧情。由于是长篇小说，各章节的情节点细纲应展现出情节的丰富性和连贯性，以适应长篇的架构。\n在进行拆解时，请仔细阅读各章节内容，梳理出每个章节主要的情节发展和关键事件。\n每条情节点细纲应简洁明了，能够概括该情节的核心内容。\n请按以下格式在<情节点细纲>标签内写下拆出的情节点细纲，每个章节的情节点细纲需分开列出，且标明对应章节顺序。\n<情节点细纲>\n【章节1】 [剧情点1] [剧情点2] [剧情点3] [剧情点4] [剧情点5] [剧情点6] [剧情点7] [剧情点8] [剧情点9] [剧情点10]\n……\n【章节10】 [剧情点1] [剧情点2] [剧情点3] [剧情点4] [剧情点5] [剧情点6] [剧情点7] [剧情点8] [剧情点9] [剧情点10]\n</情节点细纲>	2025-07-31 20:00:03.726476+00	2025-08-02 17:38:40.934404+00	拆书提示词	Haoyv Bin	60
5e1a1d3e-cb60-4d5f-83f9-8468579af161	你好,你是什么大模型	2025-08-01 06:24:32.530538+00	2025-08-02 12:55:40.947982+00	你好,你是什么大模型	Haoyv Bin	15
056c4636-14d7-4eb1-abd0-db58ad305728	官方诡异风提示词\n你是个擅长写悬疑恐怖的网络小说家，阴沉、内敛又犀利。要根据用户给的已有故事内容，续写至2000字左右未完结故事。\n主要人物信息如下：\n<main_character>\n<name>{{MAIN_CHARACTER_NAME}}</name>\n<personality>{{MAIN_CHARACTER_PERSONALITY}}</personality>\n<behavior>{{MAIN_CHARACTER_BEHAVIOR}}</behavior>\n</main_character>\n以下是用户提供的已有故事内容：\n<plot_points>\n{{PLOT_POINTS}}\n</plot_points>\n写作要求如下：\n- 运用阴寒、魔幻 - 诡异、写实 - 血痕等诡异意象。\n- 营造压抑、诡异、阴森、悬疑的氛围。\n- 以心理恐怖为主，生理恐怖为辅。\n- 暗示或明示因果关系，与过去事件或人物执念有关。\n- 将恐怖元素融入日常场景，如回家、洗澡等。\n- 逐步揭示线索，设置悬念，引导读者推测真相。\n- 语言简洁克制，用白描手法，少用修饰和堆砌形容词。\n- 分析物件或场景，设安全点并与诡异关联。\n- 基于场景或物件，设定恶鬼缘来和杀人规则。\n- 用暗处忽明忽暗的灯火投下疑虑种子。\n- 进行感官细节描写，让诡异如噩梦渗入。\n- 恐怖真相突现，打破安全点，制造绝对危险。\n\n全文禁出现“知道”“不容置疑”“一丝”等词汇。\n\n改写时要润色文本，调整语序、换词、加过渡句，让表达更自然。开篇30字内，结尾用简洁动作或对话收束，不展望未来。同时，要检查主要人物行为反应，确保性格和行为与前文一致且有合理变化。\n\n<story>\n[在此写下续写润色后的故事]\n</story>	2025-07-31 16:23:35.4702+00	2025-08-03 06:00:29.278322+00	诡异流提示词	Haoyv Bin	47
\.


--
-- Data for Name: propmt-zhanshi; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."propmt-zhanshi" (id, title, description, category, type, created_at, updated_at, created_by, author_display_id, usage_count) FROM stdin;
5e1a1d3e-cb60-4d5f-83f9-8468579af161	你好,你是什么大模型		userprompt	ai_writing	2025-08-01 06:24:32.530538+00	2025-08-02 12:55:40.947982+00	<EMAIL>	Haoyv Bin	15
324b290f-c303-4395-b104-34f304c7bfa5	拆书提示词		userprompt	ai_writing	2025-07-31 20:00:03.726476+00	2025-08-02 17:38:40.934404+00	<EMAIL>	Haoyv Bin	60
056c4636-14d7-4eb1-abd0-db58ad305728	诡异流提示词	诡异流提示词	userprompt	ai_writing	2025-07-31 16:23:35.4702+00	2025-08-03 06:00:29.278322+00	<EMAIL>	Haoyv Bin	47
\.


--
-- Data for Name: schema_migrations; Type: TABLE DATA; Schema: realtime; Owner: supabase_admin
--

COPY realtime.schema_migrations (version, inserted_at) FROM stdin;
20211116024918	2025-08-03 05:41:01
20211116045059	2025-08-03 05:41:01
20211116050929	2025-08-03 05:41:01
20211116051442	2025-08-03 05:41:01
20211116212300	2025-08-03 05:41:01
20211116213355	2025-08-03 05:41:01
20211116213934	2025-08-03 05:41:01
20211116214523	2025-08-03 05:41:01
20211122062447	2025-08-03 05:41:01
20211124070109	2025-08-03 05:41:01
20211202204204	2025-08-03 05:41:01
20211202204605	2025-08-03 05:41:01
20211210212804	2025-08-03 05:41:01
20211228014915	2025-08-03 05:41:01
20220107221237	2025-08-03 05:41:01
20220228202821	2025-08-03 05:41:01
20220312004840	2025-08-03 05:41:01
20220603231003	2025-08-03 05:41:01
20220603232444	2025-08-03 05:41:01
20220615214548	2025-08-03 05:41:01
20220712093339	2025-08-03 05:41:01
20220908172859	2025-08-03 05:41:01
20220916233421	2025-08-03 05:41:01
20230119133233	2025-08-03 05:41:01
20230128025114	2025-08-03 05:41:01
20230128025212	2025-08-03 05:41:01
20230227211149	2025-08-03 05:41:01
20230228184745	2025-08-03 05:41:01
20230308225145	2025-08-03 05:41:01
20230328144023	2025-08-03 05:41:01
20231018144023	2025-08-03 05:41:01
20231204144023	2025-08-03 05:41:01
20231204144024	2025-08-03 05:41:01
20231204144025	2025-08-03 05:41:01
20240108234812	2025-08-03 05:41:02
20240109165339	2025-08-03 05:41:02
20240227174441	2025-08-03 05:41:02
20240311171622	2025-08-03 05:41:02
20240321100241	2025-08-03 05:41:02
20240401105812	2025-08-03 05:41:02
20240418121054	2025-08-03 05:41:02
20240523004032	2025-08-03 05:41:02
20240618124746	2025-08-03 05:41:02
20240801235015	2025-08-03 05:41:02
20240805133720	2025-08-03 05:41:02
20240827160934	2025-08-03 05:41:02
20240919163303	2025-08-03 05:41:02
20240919163305	2025-08-03 05:41:02
20241019105805	2025-08-03 05:41:02
20241030150047	2025-08-03 05:41:02
20241108114728	2025-08-03 05:41:02
20241121104152	2025-08-03 05:41:02
20241130184212	2025-08-03 05:41:02
20241220035512	2025-08-03 05:41:02
20241220123912	2025-08-03 05:41:02
20241224161212	2025-08-03 05:41:02
20250107150512	2025-08-03 05:41:02
20250110162412	2025-08-03 05:41:02
20250123174212	2025-08-03 05:41:02
20250128220012	2025-08-03 05:41:02
\.


--
-- Data for Name: subscription; Type: TABLE DATA; Schema: realtime; Owner: supabase_admin
--

COPY realtime.subscription (id, subscription_id, entity, filters, claims, created_at) FROM stdin;
\.


--
-- Data for Name: buckets; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

COPY storage.buckets (id, name, owner, created_at, updated_at, public, avif_autodetection, file_size_limit, allowed_mime_types, owner_id, type) FROM stdin;
\.


--
-- Data for Name: buckets_analytics; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

COPY storage.buckets_analytics (id, type, format, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: iceberg_namespaces; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

COPY storage.iceberg_namespaces (id, bucket_id, name, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: iceberg_tables; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

COPY storage.iceberg_tables (id, namespace_id, bucket_id, name, location, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: migrations; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

COPY storage.migrations (id, name, hash, executed_at) FROM stdin;
0	create-migrations-table	e18db593bcde2aca2a408c4d1100f6abba2195df	2025-08-03 05:40:47.11591
1	initialmigration	6ab16121fbaa08bbd11b712d05f358f9b555d777	2025-08-03 05:40:47.140318
2	storage-schema	5c7968fd083fcea04050c1b7f6253c9771b99011	2025-08-03 05:40:47.14671
3	pathtoken-column	2cb1b0004b817b29d5b0a971af16bafeede4b70d	2025-08-03 05:40:47.204941
4	add-migrations-rls	427c5b63fe1c5937495d9c635c263ee7a5905058	2025-08-03 05:40:47.251241
5	add-size-functions	79e081a1455b63666c1294a440f8ad4b1e6a7f84	2025-08-03 05:40:47.270029
6	change-column-name-in-get-size	f93f62afdf6613ee5e7e815b30d02dc990201044	2025-08-03 05:40:47.295487
7	add-rls-to-buckets	e7e7f86adbc51049f341dfe8d30256c1abca17aa	2025-08-03 05:40:47.305778
8	add-public-to-buckets	fd670db39ed65f9d08b01db09d6202503ca2bab3	2025-08-03 05:40:47.334173
9	fix-search-function	3a0af29f42e35a4d101c259ed955b67e1bee6825	2025-08-03 05:40:47.342111
10	search-files-search-function	68dc14822daad0ffac3746a502234f486182ef6e	2025-08-03 05:40:47.350615
11	add-trigger-to-auto-update-updated_at-column	7425bdb14366d1739fa8a18c83100636d74dcaa2	2025-08-03 05:40:47.358488
12	add-automatic-avif-detection-flag	8e92e1266eb29518b6a4c5313ab8f29dd0d08df9	2025-08-03 05:40:47.374133
13	add-bucket-custom-limits	cce962054138135cd9a8c4bcd531598684b25e7d	2025-08-03 05:40:47.388336
14	use-bytes-for-max-size	941c41b346f9802b411f06f30e972ad4744dad27	2025-08-03 05:40:47.391723
15	add-can-insert-object-function	934146bc38ead475f4ef4b555c524ee5d66799e5	2025-08-03 05:40:47.462245
16	add-version	76debf38d3fd07dcfc747ca49096457d95b1221b	2025-08-03 05:40:47.476863
17	drop-owner-foreign-key	f1cbb288f1b7a4c1eb8c38504b80ae2a0153d101	2025-08-03 05:40:47.486802
18	add_owner_id_column_deprecate_owner	e7a511b379110b08e2f214be852c35414749fe66	2025-08-03 05:40:47.498541
19	alter-default-value-objects-id	02e5e22a78626187e00d173dc45f58fa66a4f043	2025-08-03 05:40:47.540189
20	list-objects-with-delimiter	cd694ae708e51ba82bf012bba00caf4f3b6393b7	2025-08-03 05:40:47.560633
21	s3-multipart-uploads	8c804d4a566c40cd1e4cc5b3725a664a9303657f	2025-08-03 05:40:47.589158
22	s3-multipart-uploads-big-ints	9737dc258d2397953c9953d9b86920b8be0cdb73	2025-08-03 05:40:47.651054
23	optimize-search-function	9d7e604cddc4b56a5422dc68c9313f4a1b6f132c	2025-08-03 05:40:47.68004
24	operation-function	8312e37c2bf9e76bbe841aa5fda889206d2bf8aa	2025-08-03 05:40:47.683221
25	custom-metadata	d974c6057c3db1c1f847afa0e291e6165693b990	2025-08-03 05:40:47.68893
26	objects-prefixes	ef3f7871121cdc47a65308e6702519e853422ae2	2025-08-03 05:40:47.694048
27	search-v2	33b8f2a7ae53105f028e13e9fcda9dc4f356b4a2	2025-08-03 05:40:47.73676
28	object-bucket-name-sorting	ba85ec41b62c6a30a3f136788227ee47f311c436	2025-08-03 05:40:47.8145
29	create-prefixes	a7b1a22c0dc3ab630e3055bfec7ce7d2045c5b7b	2025-08-03 05:40:47.838507
30	update-object-levels	6c6f6cc9430d570f26284a24cf7b210599032db7	2025-08-03 05:40:47.841454
31	objects-level-index	33f1fef7ec7fea08bb892222f4f0f5d79bab5eb8	2025-08-03 05:40:47.852688
32	backward-compatible-index-on-objects	2d51eeb437a96868b36fcdfb1ddefdf13bef1647	2025-08-03 05:40:47.865294
33	backward-compatible-index-on-prefixes	fe473390e1b8c407434c0e470655945b110507bf	2025-08-03 05:40:47.872741
34	optimize-search-function-v1	82b0e469a00e8ebce495e29bfa70a0797f7ebd2c	2025-08-03 05:40:47.873471
35	add-insert-trigger-prefixes	63bb9fd05deb3dc5e9fa66c83e82b152f0caf589	2025-08-03 05:40:47.882562
36	optimise-existing-functions	81cf92eb0c36612865a18016a38496c530443899	2025-08-03 05:40:47.88642
37	add-bucket-name-length-trigger	3944135b4e3e8b22d6d4cbb568fe3b0b51df15c1	2025-08-03 05:40:47.897759
38	iceberg-catalog-flag-on-buckets	19a8bd89d5dfa69af7f222a46c726b7c41e462c5	2025-08-03 05:40:47.903752
\.


--
-- Data for Name: objects; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

COPY storage.objects (id, bucket_id, name, owner, created_at, updated_at, last_accessed_at, metadata, version, owner_id, user_metadata, level) FROM stdin;
\.


--
-- Data for Name: prefixes; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

COPY storage.prefixes (bucket_id, name, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: s3_multipart_uploads; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

COPY storage.s3_multipart_uploads (id, in_progress_size, upload_signature, bucket_id, key, version, owner_id, created_at, user_metadata) FROM stdin;
\.


--
-- Data for Name: s3_multipart_uploads_parts; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

COPY storage.s3_multipart_uploads_parts (id, upload_id, size, part_number, bucket_id, key, etag, owner_id, version, created_at) FROM stdin;
\.


--
-- Data for Name: hooks; Type: TABLE DATA; Schema: supabase_functions; Owner: supabase_functions_admin
--

COPY supabase_functions.hooks (id, hook_table_id, hook_name, created_at, request_id) FROM stdin;
\.


--
-- Data for Name: migrations; Type: TABLE DATA; Schema: supabase_functions; Owner: supabase_functions_admin
--

COPY supabase_functions.migrations (version, inserted_at) FROM stdin;
initial	2025-08-03 05:39:59.261965+00
20210809183423_update_grants	2025-08-03 05:39:59.261965+00
\.


--
-- Data for Name: secrets; Type: TABLE DATA; Schema: vault; Owner: supabase_admin
--

COPY vault.secrets (id, name, description, secret, key_id, nonce, created_at, updated_at) FROM stdin;
\.


--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE SET; Schema: auth; Owner: supabase_auth_admin
--

SELECT pg_catalog.setval('auth.refresh_tokens_id_seq', 335, true);


--
-- Name: jobid_seq; Type: SEQUENCE SET; Schema: cron; Owner: supabase_admin
--

SELECT pg_catalog.setval('cron.jobid_seq', 1, false);


--
-- Name: runid_seq; Type: SEQUENCE SET; Schema: cron; Owner: supabase_admin
--

SELECT pg_catalog.setval('cron.runid_seq', 1, false);


--
-- Name: subscription_id_seq; Type: SEQUENCE SET; Schema: realtime; Owner: supabase_admin
--

SELECT pg_catalog.setval('realtime.subscription_id_seq', 1, false);


--
-- Name: hooks_id_seq; Type: SEQUENCE SET; Schema: supabase_functions; Owner: supabase_functions_admin
--

SELECT pg_catalog.setval('supabase_functions.hooks_id_seq', 1, false);


--
-- Name: extensions extensions_pkey; Type: CONSTRAINT; Schema: _realtime; Owner: supabase_admin
--

ALTER TABLE ONLY _realtime.extensions
    ADD CONSTRAINT extensions_pkey PRIMARY KEY (id);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: _realtime; Owner: supabase_admin
--

ALTER TABLE ONLY _realtime.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: tenants tenants_pkey; Type: CONSTRAINT; Schema: _realtime; Owner: supabase_admin
--

ALTER TABLE ONLY _realtime.tenants
    ADD CONSTRAINT tenants_pkey PRIMARY KEY (id);


--
-- Name: mfa_amr_claims amr_id_pk; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_amr_claims
    ADD CONSTRAINT amr_id_pk PRIMARY KEY (id);


--
-- Name: audit_log_entries audit_log_entries_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.audit_log_entries
    ADD CONSTRAINT audit_log_entries_pkey PRIMARY KEY (id);


--
-- Name: flow_state flow_state_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.flow_state
    ADD CONSTRAINT flow_state_pkey PRIMARY KEY (id);


--
-- Name: identities identities_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.identities
    ADD CONSTRAINT identities_pkey PRIMARY KEY (id);


--
-- Name: identities identities_provider_id_provider_unique; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.identities
    ADD CONSTRAINT identities_provider_id_provider_unique UNIQUE (provider_id, provider);


--
-- Name: instances instances_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.instances
    ADD CONSTRAINT instances_pkey PRIMARY KEY (id);


--
-- Name: mfa_amr_claims mfa_amr_claims_session_id_authentication_method_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_amr_claims
    ADD CONSTRAINT mfa_amr_claims_session_id_authentication_method_pkey UNIQUE (session_id, authentication_method);


--
-- Name: mfa_challenges mfa_challenges_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_challenges
    ADD CONSTRAINT mfa_challenges_pkey PRIMARY KEY (id);


--
-- Name: mfa_factors mfa_factors_last_challenged_at_key; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_factors
    ADD CONSTRAINT mfa_factors_last_challenged_at_key UNIQUE (last_challenged_at);


--
-- Name: mfa_factors mfa_factors_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_factors
    ADD CONSTRAINT mfa_factors_pkey PRIMARY KEY (id);


--
-- Name: one_time_tokens one_time_tokens_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.one_time_tokens
    ADD CONSTRAINT one_time_tokens_pkey PRIMARY KEY (id);


--
-- Name: refresh_tokens refresh_tokens_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.refresh_tokens
    ADD CONSTRAINT refresh_tokens_pkey PRIMARY KEY (id);


--
-- Name: refresh_tokens refresh_tokens_token_unique; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.refresh_tokens
    ADD CONSTRAINT refresh_tokens_token_unique UNIQUE (token);


--
-- Name: saml_providers saml_providers_entity_id_key; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.saml_providers
    ADD CONSTRAINT saml_providers_entity_id_key UNIQUE (entity_id);


--
-- Name: saml_providers saml_providers_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.saml_providers
    ADD CONSTRAINT saml_providers_pkey PRIMARY KEY (id);


--
-- Name: saml_relay_states saml_relay_states_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.saml_relay_states
    ADD CONSTRAINT saml_relay_states_pkey PRIMARY KEY (id);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: sessions sessions_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.sessions
    ADD CONSTRAINT sessions_pkey PRIMARY KEY (id);


--
-- Name: sso_domains sso_domains_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.sso_domains
    ADD CONSTRAINT sso_domains_pkey PRIMARY KEY (id);


--
-- Name: sso_providers sso_providers_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.sso_providers
    ADD CONSTRAINT sso_providers_pkey PRIMARY KEY (id);


--
-- Name: users users_phone_key; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.users
    ADD CONSTRAINT users_phone_key UNIQUE (phone);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: membership-look membership-look_email_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."membership-look"
    ADD CONSTRAINT "membership-look_email_key" UNIQUE (email);


--
-- Name: membership-look membership-look_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."membership-look"
    ADD CONSTRAINT "membership-look_pkey" PRIMARY KEY (id);


--
-- Name: membership-true membership_email_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."membership-true"
    ADD CONSTRAINT membership_email_key UNIQUE (email);


--
-- Name: membership-true membership_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."membership-true"
    ADD CONSTRAINT membership_pkey PRIMARY KEY (id);


--
-- Name: novel_files novel_files_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.novel_files
    ADD CONSTRAINT novel_files_pkey PRIMARY KEY (id);


--
-- Name: propmt-neirong propmt-neirong_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."propmt-neirong"
    ADD CONSTRAINT "propmt-neirong_pkey" PRIMARY KEY (prompt_id);


--
-- Name: propmt-zhanshi propmt-zhanshi_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."propmt-zhanshi"
    ADD CONSTRAINT "propmt-zhanshi_pkey" PRIMARY KEY (id);


--
-- Name: messages messages_pkey; Type: CONSTRAINT; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER TABLE ONLY realtime.messages
    ADD CONSTRAINT messages_pkey PRIMARY KEY (id, inserted_at);


--
-- Name: subscription pk_subscription; Type: CONSTRAINT; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.subscription
    ADD CONSTRAINT pk_subscription PRIMARY KEY (id);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: buckets_analytics buckets_analytics_pkey; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.buckets_analytics
    ADD CONSTRAINT buckets_analytics_pkey PRIMARY KEY (id);


--
-- Name: buckets buckets_pkey; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.buckets
    ADD CONSTRAINT buckets_pkey PRIMARY KEY (id);


--
-- Name: iceberg_namespaces iceberg_namespaces_pkey; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.iceberg_namespaces
    ADD CONSTRAINT iceberg_namespaces_pkey PRIMARY KEY (id);


--
-- Name: iceberg_tables iceberg_tables_pkey; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.iceberg_tables
    ADD CONSTRAINT iceberg_tables_pkey PRIMARY KEY (id);


--
-- Name: migrations migrations_name_key; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.migrations
    ADD CONSTRAINT migrations_name_key UNIQUE (name);


--
-- Name: migrations migrations_pkey; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.migrations
    ADD CONSTRAINT migrations_pkey PRIMARY KEY (id);


--
-- Name: objects objects_pkey; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.objects
    ADD CONSTRAINT objects_pkey PRIMARY KEY (id);


--
-- Name: prefixes prefixes_pkey; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.prefixes
    ADD CONSTRAINT prefixes_pkey PRIMARY KEY (bucket_id, level, name);


--
-- Name: s3_multipart_uploads_parts s3_multipart_uploads_parts_pkey; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.s3_multipart_uploads_parts
    ADD CONSTRAINT s3_multipart_uploads_parts_pkey PRIMARY KEY (id);


--
-- Name: s3_multipart_uploads s3_multipart_uploads_pkey; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.s3_multipart_uploads
    ADD CONSTRAINT s3_multipart_uploads_pkey PRIMARY KEY (id);


--
-- Name: hooks hooks_pkey; Type: CONSTRAINT; Schema: supabase_functions; Owner: supabase_functions_admin
--

ALTER TABLE ONLY supabase_functions.hooks
    ADD CONSTRAINT hooks_pkey PRIMARY KEY (id);


--
-- Name: migrations migrations_pkey; Type: CONSTRAINT; Schema: supabase_functions; Owner: supabase_functions_admin
--

ALTER TABLE ONLY supabase_functions.migrations
    ADD CONSTRAINT migrations_pkey PRIMARY KEY (version);


--
-- Name: extensions_tenant_external_id_index; Type: INDEX; Schema: _realtime; Owner: supabase_admin
--

CREATE INDEX extensions_tenant_external_id_index ON _realtime.extensions USING btree (tenant_external_id);


--
-- Name: extensions_tenant_external_id_type_index; Type: INDEX; Schema: _realtime; Owner: supabase_admin
--

CREATE UNIQUE INDEX extensions_tenant_external_id_type_index ON _realtime.extensions USING btree (tenant_external_id, type);


--
-- Name: tenants_external_id_index; Type: INDEX; Schema: _realtime; Owner: supabase_admin
--

CREATE UNIQUE INDEX tenants_external_id_index ON _realtime.tenants USING btree (external_id);


--
-- Name: audit_logs_instance_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX audit_logs_instance_id_idx ON auth.audit_log_entries USING btree (instance_id);


--
-- Name: confirmation_token_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX confirmation_token_idx ON auth.users USING btree (confirmation_token) WHERE ((confirmation_token)::text !~ '^[0-9 ]*$'::text);


--
-- Name: email_change_token_current_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX email_change_token_current_idx ON auth.users USING btree (email_change_token_current) WHERE ((email_change_token_current)::text !~ '^[0-9 ]*$'::text);


--
-- Name: email_change_token_new_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX email_change_token_new_idx ON auth.users USING btree (email_change_token_new) WHERE ((email_change_token_new)::text !~ '^[0-9 ]*$'::text);


--
-- Name: factor_id_created_at_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX factor_id_created_at_idx ON auth.mfa_factors USING btree (user_id, created_at);


--
-- Name: flow_state_created_at_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX flow_state_created_at_idx ON auth.flow_state USING btree (created_at DESC);


--
-- Name: identities_email_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX identities_email_idx ON auth.identities USING btree (email text_pattern_ops);


--
-- Name: INDEX identities_email_idx; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON INDEX auth.identities_email_idx IS 'Auth: Ensures indexed queries on the email column';


--
-- Name: identities_user_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX identities_user_id_idx ON auth.identities USING btree (user_id);


--
-- Name: idx_auth_code; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX idx_auth_code ON auth.flow_state USING btree (auth_code);


--
-- Name: idx_user_id_auth_method; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX idx_user_id_auth_method ON auth.flow_state USING btree (user_id, authentication_method);


--
-- Name: mfa_challenge_created_at_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX mfa_challenge_created_at_idx ON auth.mfa_challenges USING btree (created_at DESC);


--
-- Name: mfa_factors_user_friendly_name_unique; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX mfa_factors_user_friendly_name_unique ON auth.mfa_factors USING btree (friendly_name, user_id) WHERE (TRIM(BOTH FROM friendly_name) <> ''::text);


--
-- Name: mfa_factors_user_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX mfa_factors_user_id_idx ON auth.mfa_factors USING btree (user_id);


--
-- Name: one_time_tokens_relates_to_hash_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX one_time_tokens_relates_to_hash_idx ON auth.one_time_tokens USING hash (relates_to);


--
-- Name: one_time_tokens_token_hash_hash_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX one_time_tokens_token_hash_hash_idx ON auth.one_time_tokens USING hash (token_hash);


--
-- Name: one_time_tokens_user_id_token_type_key; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX one_time_tokens_user_id_token_type_key ON auth.one_time_tokens USING btree (user_id, token_type);


--
-- Name: reauthentication_token_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX reauthentication_token_idx ON auth.users USING btree (reauthentication_token) WHERE ((reauthentication_token)::text !~ '^[0-9 ]*$'::text);


--
-- Name: recovery_token_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX recovery_token_idx ON auth.users USING btree (recovery_token) WHERE ((recovery_token)::text !~ '^[0-9 ]*$'::text);


--
-- Name: refresh_tokens_instance_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX refresh_tokens_instance_id_idx ON auth.refresh_tokens USING btree (instance_id);


--
-- Name: refresh_tokens_instance_id_user_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX refresh_tokens_instance_id_user_id_idx ON auth.refresh_tokens USING btree (instance_id, user_id);


--
-- Name: refresh_tokens_parent_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX refresh_tokens_parent_idx ON auth.refresh_tokens USING btree (parent);


--
-- Name: refresh_tokens_session_id_revoked_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX refresh_tokens_session_id_revoked_idx ON auth.refresh_tokens USING btree (session_id, revoked);


--
-- Name: refresh_tokens_updated_at_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX refresh_tokens_updated_at_idx ON auth.refresh_tokens USING btree (updated_at DESC);


--
-- Name: saml_providers_sso_provider_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX saml_providers_sso_provider_id_idx ON auth.saml_providers USING btree (sso_provider_id);


--
-- Name: saml_relay_states_created_at_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX saml_relay_states_created_at_idx ON auth.saml_relay_states USING btree (created_at DESC);


--
-- Name: saml_relay_states_for_email_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX saml_relay_states_for_email_idx ON auth.saml_relay_states USING btree (for_email);


--
-- Name: saml_relay_states_sso_provider_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX saml_relay_states_sso_provider_id_idx ON auth.saml_relay_states USING btree (sso_provider_id);


--
-- Name: sessions_not_after_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX sessions_not_after_idx ON auth.sessions USING btree (not_after DESC);


--
-- Name: sessions_user_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX sessions_user_id_idx ON auth.sessions USING btree (user_id);


--
-- Name: sso_domains_domain_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX sso_domains_domain_idx ON auth.sso_domains USING btree (lower(domain));


--
-- Name: sso_domains_sso_provider_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX sso_domains_sso_provider_id_idx ON auth.sso_domains USING btree (sso_provider_id);


--
-- Name: sso_providers_resource_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX sso_providers_resource_id_idx ON auth.sso_providers USING btree (lower(resource_id));


--
-- Name: unique_phone_factor_per_user; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX unique_phone_factor_per_user ON auth.mfa_factors USING btree (user_id, phone);


--
-- Name: user_id_created_at_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX user_id_created_at_idx ON auth.sessions USING btree (user_id, created_at);


--
-- Name: users_email_partial_key; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX users_email_partial_key ON auth.users USING btree (email) WHERE (is_sso_user = false);


--
-- Name: INDEX users_email_partial_key; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON INDEX auth.users_email_partial_key IS 'Auth: A partial unique index that applies only when is_sso_user is false';


--
-- Name: users_instance_id_email_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX users_instance_id_email_idx ON auth.users USING btree (instance_id, lower((email)::text));


--
-- Name: users_instance_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX users_instance_id_idx ON auth.users USING btree (instance_id);


--
-- Name: users_is_anonymous_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX users_is_anonymous_idx ON auth.users USING btree (is_anonymous);


--
-- Name: idx_membership_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_membership_user_id ON public."membership-true" USING btree (user_id);


--
-- Name: idx_novel_files_minio_object_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_novel_files_minio_object_key ON public.novel_files USING btree (minio_object_key);


--
-- Name: idx_novel_files_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_novel_files_user_id ON public.novel_files USING btree (user_id);


--
-- Name: idx_propmt_neirong_prompt_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_propmt_neirong_prompt_id ON public."propmt-neirong" USING btree (prompt_id);


--
-- Name: idx_propmt_neirong_usage_count; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_propmt_neirong_usage_count ON public."propmt-neirong" USING btree (usage_count DESC);


--
-- Name: idx_propmt_zhanshi_category; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_propmt_zhanshi_category ON public."propmt-zhanshi" USING btree (category);


--
-- Name: idx_propmt_zhanshi_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_propmt_zhanshi_type ON public."propmt-zhanshi" USING btree (type);


--
-- Name: idx_propmt_zhanshi_usage_count; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_propmt_zhanshi_usage_count ON public."propmt-zhanshi" USING btree (usage_count DESC);


--
-- Name: membership-look_user_id_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "membership-look_user_id_idx" ON public."membership-look" USING btree (user_id);


--
-- Name: ix_realtime_subscription_entity; Type: INDEX; Schema: realtime; Owner: supabase_admin
--

CREATE INDEX ix_realtime_subscription_entity ON realtime.subscription USING btree (entity);


--
-- Name: subscription_subscription_id_entity_filters_key; Type: INDEX; Schema: realtime; Owner: supabase_admin
--

CREATE UNIQUE INDEX subscription_subscription_id_entity_filters_key ON realtime.subscription USING btree (subscription_id, entity, filters);


--
-- Name: bname; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE UNIQUE INDEX bname ON storage.buckets USING btree (name);


--
-- Name: bucketid_objname; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE UNIQUE INDEX bucketid_objname ON storage.objects USING btree (bucket_id, name);


--
-- Name: idx_iceberg_namespaces_bucket_id; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE UNIQUE INDEX idx_iceberg_namespaces_bucket_id ON storage.iceberg_namespaces USING btree (bucket_id, name);


--
-- Name: idx_iceberg_tables_namespace_id; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE UNIQUE INDEX idx_iceberg_tables_namespace_id ON storage.iceberg_tables USING btree (namespace_id, name);


--
-- Name: idx_multipart_uploads_list; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE INDEX idx_multipart_uploads_list ON storage.s3_multipart_uploads USING btree (bucket_id, key, created_at);


--
-- Name: idx_name_bucket_level_unique; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE UNIQUE INDEX idx_name_bucket_level_unique ON storage.objects USING btree (name COLLATE "C", bucket_id, level);


--
-- Name: idx_objects_bucket_id_name; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE INDEX idx_objects_bucket_id_name ON storage.objects USING btree (bucket_id, name COLLATE "C");


--
-- Name: idx_objects_lower_name; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE INDEX idx_objects_lower_name ON storage.objects USING btree ((path_tokens[level]), lower(name) text_pattern_ops, bucket_id, level);


--
-- Name: idx_prefixes_lower_name; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE INDEX idx_prefixes_lower_name ON storage.prefixes USING btree (bucket_id, level, ((string_to_array(name, '/'::text))[level]), lower(name) text_pattern_ops);


--
-- Name: name_prefix_search; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE INDEX name_prefix_search ON storage.objects USING btree (name text_pattern_ops);


--
-- Name: objects_bucket_id_level_idx; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE UNIQUE INDEX objects_bucket_id_level_idx ON storage.objects USING btree (bucket_id, level, name COLLATE "C");


--
-- Name: supabase_functions_hooks_h_table_id_h_name_idx; Type: INDEX; Schema: supabase_functions; Owner: supabase_functions_admin
--

CREATE INDEX supabase_functions_hooks_h_table_id_h_name_idx ON supabase_functions.hooks USING btree (hook_table_id, hook_name);


--
-- Name: supabase_functions_hooks_request_id_idx; Type: INDEX; Schema: supabase_functions; Owner: supabase_functions_admin
--

CREATE INDEX supabase_functions_hooks_request_id_idx ON supabase_functions.hooks USING btree (request_id);


--
-- Name: propmt-neirong set_initial_title_trigger; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER set_initial_title_trigger BEFORE INSERT ON public."propmt-neirong" FOR EACH ROW EXECUTE FUNCTION public.set_initial_title();


--
-- Name: membership-true sync_membership_trigger; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER sync_membership_trigger AFTER INSERT OR DELETE OR UPDATE ON public."membership-true" FOR EACH ROW EXECUTE FUNCTION public.sync_membership_to_look();


--
-- Name: propmt-zhanshi sync_title_trigger; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER sync_title_trigger AFTER UPDATE ON public."propmt-zhanshi" FOR EACH ROW EXECUTE FUNCTION public.sync_prompt_title();


--
-- Name: propmt-neirong trigger_sync_usage_count_to_zhanshi; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER trigger_sync_usage_count_to_zhanshi AFTER INSERT OR UPDATE ON public."propmt-neirong" FOR EACH ROW EXECUTE FUNCTION public.sync_usage_count_to_zhanshi();


--
-- Name: subscription tr_check_filters; Type: TRIGGER; Schema: realtime; Owner: supabase_admin
--

CREATE TRIGGER tr_check_filters BEFORE INSERT OR UPDATE ON realtime.subscription FOR EACH ROW EXECUTE FUNCTION realtime.subscription_check_filters();


--
-- Name: buckets enforce_bucket_name_length_trigger; Type: TRIGGER; Schema: storage; Owner: supabase_storage_admin
--

CREATE TRIGGER enforce_bucket_name_length_trigger BEFORE INSERT OR UPDATE OF name ON storage.buckets FOR EACH ROW EXECUTE FUNCTION storage.enforce_bucket_name_length();


--
-- Name: objects objects_delete_delete_prefix; Type: TRIGGER; Schema: storage; Owner: supabase_storage_admin
--

CREATE TRIGGER objects_delete_delete_prefix AFTER DELETE ON storage.objects FOR EACH ROW EXECUTE FUNCTION storage.delete_prefix_hierarchy_trigger();


--
-- Name: objects objects_insert_create_prefix; Type: TRIGGER; Schema: storage; Owner: supabase_storage_admin
--

CREATE TRIGGER objects_insert_create_prefix BEFORE INSERT ON storage.objects FOR EACH ROW EXECUTE FUNCTION storage.objects_insert_prefix_trigger();


--
-- Name: objects objects_update_create_prefix; Type: TRIGGER; Schema: storage; Owner: supabase_storage_admin
--

CREATE TRIGGER objects_update_create_prefix BEFORE UPDATE ON storage.objects FOR EACH ROW WHEN (((new.name <> old.name) OR (new.bucket_id <> old.bucket_id))) EXECUTE FUNCTION storage.objects_update_prefix_trigger();


--
-- Name: prefixes prefixes_create_hierarchy; Type: TRIGGER; Schema: storage; Owner: supabase_storage_admin
--

CREATE TRIGGER prefixes_create_hierarchy BEFORE INSERT ON storage.prefixes FOR EACH ROW WHEN ((pg_trigger_depth() < 1)) EXECUTE FUNCTION storage.prefixes_insert_trigger();


--
-- Name: prefixes prefixes_delete_hierarchy; Type: TRIGGER; Schema: storage; Owner: supabase_storage_admin
--

CREATE TRIGGER prefixes_delete_hierarchy AFTER DELETE ON storage.prefixes FOR EACH ROW EXECUTE FUNCTION storage.delete_prefix_hierarchy_trigger();


--
-- Name: objects update_objects_updated_at; Type: TRIGGER; Schema: storage; Owner: supabase_storage_admin
--

CREATE TRIGGER update_objects_updated_at BEFORE UPDATE ON storage.objects FOR EACH ROW EXECUTE FUNCTION storage.update_updated_at_column();


--
-- Name: extensions extensions_tenant_external_id_fkey; Type: FK CONSTRAINT; Schema: _realtime; Owner: supabase_admin
--

ALTER TABLE ONLY _realtime.extensions
    ADD CONSTRAINT extensions_tenant_external_id_fkey FOREIGN KEY (tenant_external_id) REFERENCES _realtime.tenants(external_id) ON DELETE CASCADE;


--
-- Name: identities identities_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.identities
    ADD CONSTRAINT identities_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: mfa_amr_claims mfa_amr_claims_session_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_amr_claims
    ADD CONSTRAINT mfa_amr_claims_session_id_fkey FOREIGN KEY (session_id) REFERENCES auth.sessions(id) ON DELETE CASCADE;


--
-- Name: mfa_challenges mfa_challenges_auth_factor_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_challenges
    ADD CONSTRAINT mfa_challenges_auth_factor_id_fkey FOREIGN KEY (factor_id) REFERENCES auth.mfa_factors(id) ON DELETE CASCADE;


--
-- Name: mfa_factors mfa_factors_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_factors
    ADD CONSTRAINT mfa_factors_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: one_time_tokens one_time_tokens_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.one_time_tokens
    ADD CONSTRAINT one_time_tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: refresh_tokens refresh_tokens_session_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.refresh_tokens
    ADD CONSTRAINT refresh_tokens_session_id_fkey FOREIGN KEY (session_id) REFERENCES auth.sessions(id) ON DELETE CASCADE;


--
-- Name: saml_providers saml_providers_sso_provider_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.saml_providers
    ADD CONSTRAINT saml_providers_sso_provider_id_fkey FOREIGN KEY (sso_provider_id) REFERENCES auth.sso_providers(id) ON DELETE CASCADE;


--
-- Name: saml_relay_states saml_relay_states_flow_state_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.saml_relay_states
    ADD CONSTRAINT saml_relay_states_flow_state_id_fkey FOREIGN KEY (flow_state_id) REFERENCES auth.flow_state(id) ON DELETE CASCADE;


--
-- Name: saml_relay_states saml_relay_states_sso_provider_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.saml_relay_states
    ADD CONSTRAINT saml_relay_states_sso_provider_id_fkey FOREIGN KEY (sso_provider_id) REFERENCES auth.sso_providers(id) ON DELETE CASCADE;


--
-- Name: sessions sessions_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.sessions
    ADD CONSTRAINT sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: sso_domains sso_domains_sso_provider_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.sso_domains
    ADD CONSTRAINT sso_domains_sso_provider_id_fkey FOREIGN KEY (sso_provider_id) REFERENCES auth.sso_providers(id) ON DELETE CASCADE;


--
-- Name: novel_files novel_files_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.novel_files
    ADD CONSTRAINT novel_files_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: propmt-neirong propmt-neirong_prompt_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."propmt-neirong"
    ADD CONSTRAINT "propmt-neirong_prompt_id_fkey" FOREIGN KEY (prompt_id) REFERENCES public."propmt-zhanshi"(id) ON DELETE CASCADE;


--
-- Name: iceberg_namespaces iceberg_namespaces_bucket_id_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.iceberg_namespaces
    ADD CONSTRAINT iceberg_namespaces_bucket_id_fkey FOREIGN KEY (bucket_id) REFERENCES storage.buckets_analytics(id) ON DELETE CASCADE;


--
-- Name: iceberg_tables iceberg_tables_bucket_id_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.iceberg_tables
    ADD CONSTRAINT iceberg_tables_bucket_id_fkey FOREIGN KEY (bucket_id) REFERENCES storage.buckets_analytics(id) ON DELETE CASCADE;


--
-- Name: iceberg_tables iceberg_tables_namespace_id_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.iceberg_tables
    ADD CONSTRAINT iceberg_tables_namespace_id_fkey FOREIGN KEY (namespace_id) REFERENCES storage.iceberg_namespaces(id) ON DELETE CASCADE;


--
-- Name: objects objects_bucketId_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.objects
    ADD CONSTRAINT "objects_bucketId_fkey" FOREIGN KEY (bucket_id) REFERENCES storage.buckets(id);


--
-- Name: prefixes prefixes_bucketId_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.prefixes
    ADD CONSTRAINT "prefixes_bucketId_fkey" FOREIGN KEY (bucket_id) REFERENCES storage.buckets(id);


--
-- Name: s3_multipart_uploads s3_multipart_uploads_bucket_id_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.s3_multipart_uploads
    ADD CONSTRAINT s3_multipart_uploads_bucket_id_fkey FOREIGN KEY (bucket_id) REFERENCES storage.buckets(id);


--
-- Name: s3_multipart_uploads_parts s3_multipart_uploads_parts_bucket_id_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.s3_multipart_uploads_parts
    ADD CONSTRAINT s3_multipart_uploads_parts_bucket_id_fkey FOREIGN KEY (bucket_id) REFERENCES storage.buckets(id);


--
-- Name: s3_multipart_uploads_parts s3_multipart_uploads_parts_upload_id_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.s3_multipart_uploads_parts
    ADD CONSTRAINT s3_multipart_uploads_parts_upload_id_fkey FOREIGN KEY (upload_id) REFERENCES storage.s3_multipart_uploads(id) ON DELETE CASCADE;


--
-- Name: audit_log_entries; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.audit_log_entries ENABLE ROW LEVEL SECURITY;

--
-- Name: flow_state; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.flow_state ENABLE ROW LEVEL SECURITY;

--
-- Name: identities; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.identities ENABLE ROW LEVEL SECURITY;

--
-- Name: instances; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.instances ENABLE ROW LEVEL SECURITY;

--
-- Name: mfa_amr_claims; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.mfa_amr_claims ENABLE ROW LEVEL SECURITY;

--
-- Name: mfa_challenges; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.mfa_challenges ENABLE ROW LEVEL SECURITY;

--
-- Name: mfa_factors; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.mfa_factors ENABLE ROW LEVEL SECURITY;

--
-- Name: one_time_tokens; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.one_time_tokens ENABLE ROW LEVEL SECURITY;

--
-- Name: refresh_tokens; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.refresh_tokens ENABLE ROW LEVEL SECURITY;

--
-- Name: saml_providers; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.saml_providers ENABLE ROW LEVEL SECURITY;

--
-- Name: saml_relay_states; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.saml_relay_states ENABLE ROW LEVEL SECURITY;

--
-- Name: schema_migrations; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.schema_migrations ENABLE ROW LEVEL SECURITY;

--
-- Name: sessions; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.sessions ENABLE ROW LEVEL SECURITY;

--
-- Name: sso_domains; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.sso_domains ENABLE ROW LEVEL SECURITY;

--
-- Name: sso_providers; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.sso_providers ENABLE ROW LEVEL SECURITY;

--
-- Name: users; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.users ENABLE ROW LEVEL SECURITY;

--
-- Name: propmt-zhanshi Allow read access for all users; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Allow read access for all users" ON public."propmt-zhanshi" FOR SELECT USING (true);


--
-- Name: propmt-zhanshi Allow write access for specific user; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Allow write access for specific user" ON public."propmt-zhanshi" USING (((auth.jwt() ->> 'email'::text) = '<EMAIL>'::text));


--
-- Name: propmt-neirong Deny all access; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Deny all access" ON public."propmt-neirong" USING (false) WITH CHECK (false);


--
-- Name: membership-true Service role full access on membership-true; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Service role full access on membership-true" ON public."membership-true" USING ((auth.role() = 'service_role'::text));


--
-- Name: membership-look Users can view own membership-look by uuid; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Users can view own membership-look by uuid" ON public."membership-look" FOR SELECT USING ((auth.uid() = user_id));


--
-- Name: propmt-neirong allow_function_access; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY allow_function_access ON public."propmt-neirong" TO postgres USING (true) WITH CHECK (true);


--
-- Name: membership-look; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public."membership-look" ENABLE ROW LEVEL SECURITY;

--
-- Name: membership-true; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public."membership-true" ENABLE ROW LEVEL SECURITY;

--
-- Name: propmt-neirong; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public."propmt-neirong" ENABLE ROW LEVEL SECURITY;

--
-- Name: propmt-zhanshi; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public."propmt-zhanshi" ENABLE ROW LEVEL SECURITY;

--
-- Name: propmt-neirong users_can_access_own_prompt_content; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY users_can_access_own_prompt_content ON public."propmt-neirong" USING ((EXISTS ( SELECT 1
   FROM public."propmt-zhanshi"
  WHERE (("propmt-zhanshi".id = "propmt-neirong".prompt_id) AND ((("propmt-zhanshi".category)::text = 'official'::text) OR ((("propmt-zhanshi".category)::text = 'userprompt'::text) AND (("propmt-zhanshi".created_by)::text = (auth.jwt() ->> 'email'::text))))))));


--
-- Name: messages; Type: ROW SECURITY; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER TABLE realtime.messages ENABLE ROW LEVEL SECURITY;

--
-- Name: buckets; Type: ROW SECURITY; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE storage.buckets ENABLE ROW LEVEL SECURITY;

--
-- Name: buckets_analytics; Type: ROW SECURITY; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE storage.buckets_analytics ENABLE ROW LEVEL SECURITY;

--
-- Name: iceberg_namespaces; Type: ROW SECURITY; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE storage.iceberg_namespaces ENABLE ROW LEVEL SECURITY;

--
-- Name: iceberg_tables; Type: ROW SECURITY; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE storage.iceberg_tables ENABLE ROW LEVEL SECURITY;

--
-- Name: migrations; Type: ROW SECURITY; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE storage.migrations ENABLE ROW LEVEL SECURITY;

--
-- Name: objects; Type: ROW SECURITY; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

--
-- Name: prefixes; Type: ROW SECURITY; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE storage.prefixes ENABLE ROW LEVEL SECURITY;

--
-- Name: s3_multipart_uploads; Type: ROW SECURITY; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE storage.s3_multipart_uploads ENABLE ROW LEVEL SECURITY;

--
-- Name: s3_multipart_uploads_parts; Type: ROW SECURITY; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE storage.s3_multipart_uploads_parts ENABLE ROW LEVEL SECURITY;

--
-- Name: supabase_realtime; Type: PUBLICATION; Schema: -; Owner: postgres
--

CREATE PUBLICATION supabase_realtime WITH (publish = 'insert, update, delete, truncate');


ALTER PUBLICATION supabase_realtime OWNER TO postgres;

--
-- Name: SCHEMA auth; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT USAGE ON SCHEMA auth TO anon;
GRANT USAGE ON SCHEMA auth TO authenticated;
GRANT USAGE ON SCHEMA auth TO service_role;
GRANT ALL ON SCHEMA auth TO supabase_auth_admin;
GRANT ALL ON SCHEMA auth TO dashboard_user;
GRANT ALL ON SCHEMA auth TO postgres;


--
-- Name: SCHEMA cron; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT USAGE ON SCHEMA cron TO postgres WITH GRANT OPTION;


--
-- Name: SCHEMA extensions; Type: ACL; Schema: -; Owner: postgres
--

GRANT USAGE ON SCHEMA extensions TO anon;
GRANT USAGE ON SCHEMA extensions TO authenticated;
GRANT USAGE ON SCHEMA extensions TO service_role;
GRANT ALL ON SCHEMA extensions TO dashboard_user;


--
-- Name: SCHEMA net; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT USAGE ON SCHEMA net TO supabase_functions_admin;
GRANT USAGE ON SCHEMA net TO postgres;
GRANT USAGE ON SCHEMA net TO anon;
GRANT USAGE ON SCHEMA net TO authenticated;
GRANT USAGE ON SCHEMA net TO service_role;


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: pg_database_owner
--

GRANT USAGE ON SCHEMA public TO postgres;
GRANT USAGE ON SCHEMA public TO anon;
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT USAGE ON SCHEMA public TO service_role;


--
-- Name: SCHEMA realtime; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT USAGE ON SCHEMA realtime TO postgres;
GRANT USAGE ON SCHEMA realtime TO anon;
GRANT USAGE ON SCHEMA realtime TO authenticated;
GRANT USAGE ON SCHEMA realtime TO service_role;
GRANT ALL ON SCHEMA realtime TO supabase_realtime_admin;


--
-- Name: SCHEMA storage; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT ALL ON SCHEMA storage TO postgres;
GRANT USAGE ON SCHEMA storage TO anon;
GRANT USAGE ON SCHEMA storage TO authenticated;
GRANT USAGE ON SCHEMA storage TO service_role;
GRANT ALL ON SCHEMA storage TO supabase_storage_admin;
GRANT ALL ON SCHEMA storage TO dashboard_user;


--
-- Name: SCHEMA supabase_functions; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT USAGE ON SCHEMA supabase_functions TO postgres;
GRANT USAGE ON SCHEMA supabase_functions TO anon;
GRANT USAGE ON SCHEMA supabase_functions TO authenticated;
GRANT USAGE ON SCHEMA supabase_functions TO service_role;
GRANT ALL ON SCHEMA supabase_functions TO supabase_functions_admin;


--
-- Name: SCHEMA vault; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT USAGE ON SCHEMA vault TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION email(); Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON FUNCTION auth.email() TO dashboard_user;


--
-- Name: FUNCTION jwt(); Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON FUNCTION auth.jwt() TO postgres;
GRANT ALL ON FUNCTION auth.jwt() TO dashboard_user;


--
-- Name: FUNCTION role(); Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON FUNCTION auth.role() TO dashboard_user;


--
-- Name: FUNCTION uid(); Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON FUNCTION auth.uid() TO dashboard_user;


--
-- Name: FUNCTION alter_job(job_id bigint, schedule text, command text, database text, username text, active boolean); Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT ALL ON FUNCTION cron.alter_job(job_id bigint, schedule text, command text, database text, username text, active boolean) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION job_cache_invalidate(); Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT ALL ON FUNCTION cron.job_cache_invalidate() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION schedule(schedule text, command text); Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT ALL ON FUNCTION cron.schedule(schedule text, command text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION schedule(job_name text, schedule text, command text); Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT ALL ON FUNCTION cron.schedule(job_name text, schedule text, command text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION schedule_in_database(job_name text, schedule text, command text, database text, username text, active boolean); Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT ALL ON FUNCTION cron.schedule_in_database(job_name text, schedule text, command text, database text, username text, active boolean) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION unschedule(job_id bigint); Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT ALL ON FUNCTION cron.unschedule(job_id bigint) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION unschedule(job_name text); Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT ALL ON FUNCTION cron.unschedule(job_name text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION algorithm_sign(signables text, secret text, algorithm text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.algorithm_sign(signables text, secret text, algorithm text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.algorithm_sign(signables text, secret text, algorithm text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION armor(bytea); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.armor(bytea) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.armor(bytea) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION armor(bytea, text[], text[]); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.armor(bytea, text[], text[]) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.armor(bytea, text[], text[]) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION crypt(text, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.crypt(text, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.crypt(text, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION dearmor(text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.dearmor(text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.dearmor(text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION decrypt(bytea, bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.decrypt(bytea, bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.decrypt(bytea, bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION decrypt_iv(bytea, bytea, bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.decrypt_iv(bytea, bytea, bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.decrypt_iv(bytea, bytea, bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION digest(bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.digest(bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.digest(bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION digest(text, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.digest(text, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.digest(text, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION encrypt(bytea, bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.encrypt(bytea, bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.encrypt(bytea, bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION encrypt_iv(bytea, bytea, bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.encrypt_iv(bytea, bytea, bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.encrypt_iv(bytea, bytea, bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION gen_random_bytes(integer); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.gen_random_bytes(integer) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.gen_random_bytes(integer) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION gen_random_uuid(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.gen_random_uuid() TO dashboard_user;
GRANT ALL ON FUNCTION extensions.gen_random_uuid() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION gen_salt(text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.gen_salt(text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.gen_salt(text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION gen_salt(text, integer); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.gen_salt(text, integer) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.gen_salt(text, integer) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION grant_pg_cron_access(); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.grant_pg_cron_access() FROM postgres;
GRANT ALL ON FUNCTION extensions.grant_pg_cron_access() TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.grant_pg_cron_access() TO dashboard_user;


--
-- Name: FUNCTION grant_pg_graphql_access(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.grant_pg_graphql_access() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION grant_pg_net_access(); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.grant_pg_net_access() FROM postgres;
GRANT ALL ON FUNCTION extensions.grant_pg_net_access() TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.grant_pg_net_access() TO dashboard_user;


--
-- Name: FUNCTION hmac(bytea, bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.hmac(bytea, bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.hmac(bytea, bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION hmac(text, text, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.hmac(text, text, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.hmac(text, text, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pg_stat_statements(showtext boolean, OUT userid oid, OUT dbid oid, OUT toplevel boolean, OUT queryid bigint, OUT query text, OUT plans bigint, OUT total_plan_time double precision, OUT min_plan_time double precision, OUT max_plan_time double precision, OUT mean_plan_time double precision, OUT stddev_plan_time double precision, OUT calls bigint, OUT total_exec_time double precision, OUT min_exec_time double precision, OUT max_exec_time double precision, OUT mean_exec_time double precision, OUT stddev_exec_time double precision, OUT rows bigint, OUT shared_blks_hit bigint, OUT shared_blks_read bigint, OUT shared_blks_dirtied bigint, OUT shared_blks_written bigint, OUT local_blks_hit bigint, OUT local_blks_read bigint, OUT local_blks_dirtied bigint, OUT local_blks_written bigint, OUT temp_blks_read bigint, OUT temp_blks_written bigint, OUT blk_read_time double precision, OUT blk_write_time double precision, OUT temp_blk_read_time double precision, OUT temp_blk_write_time double precision, OUT wal_records bigint, OUT wal_fpi bigint, OUT wal_bytes numeric, OUT jit_functions bigint, OUT jit_generation_time double precision, OUT jit_inlining_count bigint, OUT jit_inlining_time double precision, OUT jit_optimization_count bigint, OUT jit_optimization_time double precision, OUT jit_emission_count bigint, OUT jit_emission_time double precision); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pg_stat_statements(showtext boolean, OUT userid oid, OUT dbid oid, OUT toplevel boolean, OUT queryid bigint, OUT query text, OUT plans bigint, OUT total_plan_time double precision, OUT min_plan_time double precision, OUT max_plan_time double precision, OUT mean_plan_time double precision, OUT stddev_plan_time double precision, OUT calls bigint, OUT total_exec_time double precision, OUT min_exec_time double precision, OUT max_exec_time double precision, OUT mean_exec_time double precision, OUT stddev_exec_time double precision, OUT rows bigint, OUT shared_blks_hit bigint, OUT shared_blks_read bigint, OUT shared_blks_dirtied bigint, OUT shared_blks_written bigint, OUT local_blks_hit bigint, OUT local_blks_read bigint, OUT local_blks_dirtied bigint, OUT local_blks_written bigint, OUT temp_blks_read bigint, OUT temp_blks_written bigint, OUT blk_read_time double precision, OUT blk_write_time double precision, OUT temp_blk_read_time double precision, OUT temp_blk_write_time double precision, OUT wal_records bigint, OUT wal_fpi bigint, OUT wal_bytes numeric, OUT jit_functions bigint, OUT jit_generation_time double precision, OUT jit_inlining_count bigint, OUT jit_inlining_time double precision, OUT jit_optimization_count bigint, OUT jit_optimization_time double precision, OUT jit_emission_count bigint, OUT jit_emission_time double precision) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pg_stat_statements_info(OUT dealloc bigint, OUT stats_reset timestamp with time zone); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pg_stat_statements_info(OUT dealloc bigint, OUT stats_reset timestamp with time zone) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pg_stat_statements_reset(userid oid, dbid oid, queryid bigint); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pg_stat_statements_reset(userid oid, dbid oid, queryid bigint) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_armor_headers(text, OUT key text, OUT value text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_armor_headers(text, OUT key text, OUT value text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_armor_headers(text, OUT key text, OUT value text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_key_id(bytea); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_key_id(bytea) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_key_id(bytea) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_pub_decrypt(bytea, bytea); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_pub_decrypt(bytea, bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_pub_decrypt(bytea, bytea, text, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea, text, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea, text, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_pub_decrypt_bytea(bytea, bytea); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_pub_decrypt_bytea(bytea, bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_pub_decrypt_bytea(bytea, bytea, text, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea, text, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea, text, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_pub_encrypt(text, bytea); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt(text, bytea) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt(text, bytea) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_pub_encrypt(text, bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt(text, bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt(text, bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_pub_encrypt_bytea(bytea, bytea); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt_bytea(bytea, bytea) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt_bytea(bytea, bytea) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_pub_encrypt_bytea(bytea, bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt_bytea(bytea, bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt_bytea(bytea, bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_sym_decrypt(bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt(bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt(bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_sym_decrypt(bytea, text, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt(bytea, text, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt(bytea, text, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_sym_decrypt_bytea(bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt_bytea(bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt_bytea(bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_sym_decrypt_bytea(bytea, text, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt_bytea(bytea, text, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt_bytea(bytea, text, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_sym_encrypt(text, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt(text, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt(text, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_sym_encrypt(text, text, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt(text, text, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt(text, text, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_sym_encrypt_bytea(bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt_bytea(bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt_bytea(bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_sym_encrypt_bytea(bytea, text, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt_bytea(bytea, text, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt_bytea(bytea, text, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgrst_ddl_watch(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgrst_ddl_watch() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgrst_drop_watch(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgrst_drop_watch() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION set_graphql_placeholder(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.set_graphql_placeholder() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION sign(payload json, secret text, algorithm text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.sign(payload json, secret text, algorithm text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.sign(payload json, secret text, algorithm text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION try_cast_double(inp text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.try_cast_double(inp text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.try_cast_double(inp text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION url_decode(data text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.url_decode(data text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.url_decode(data text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION url_encode(data bytea); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.url_encode(data bytea) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.url_encode(data bytea) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION uuid_generate_v1(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.uuid_generate_v1() TO dashboard_user;
GRANT ALL ON FUNCTION extensions.uuid_generate_v1() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION uuid_generate_v1mc(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.uuid_generate_v1mc() TO dashboard_user;
GRANT ALL ON FUNCTION extensions.uuid_generate_v1mc() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION uuid_generate_v3(namespace uuid, name text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.uuid_generate_v3(namespace uuid, name text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.uuid_generate_v3(namespace uuid, name text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION uuid_generate_v4(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.uuid_generate_v4() TO dashboard_user;
GRANT ALL ON FUNCTION extensions.uuid_generate_v4() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION uuid_generate_v5(namespace uuid, name text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.uuid_generate_v5(namespace uuid, name text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.uuid_generate_v5(namespace uuid, name text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION uuid_nil(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.uuid_nil() TO dashboard_user;
GRANT ALL ON FUNCTION extensions.uuid_nil() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION uuid_ns_dns(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.uuid_ns_dns() TO dashboard_user;
GRANT ALL ON FUNCTION extensions.uuid_ns_dns() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION uuid_ns_oid(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.uuid_ns_oid() TO dashboard_user;
GRANT ALL ON FUNCTION extensions.uuid_ns_oid() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION uuid_ns_url(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.uuid_ns_url() TO dashboard_user;
GRANT ALL ON FUNCTION extensions.uuid_ns_url() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION uuid_ns_x500(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.uuid_ns_x500() TO dashboard_user;
GRANT ALL ON FUNCTION extensions.uuid_ns_x500() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION verify(token text, secret text, algorithm text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.verify(token text, secret text, algorithm text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.verify(token text, secret text, algorithm text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION graphql("operationName" text, query text, variables jsonb, extensions jsonb); Type: ACL; Schema: graphql_public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION graphql_public.graphql("operationName" text, query text, variables jsonb, extensions jsonb) TO postgres;
GRANT ALL ON FUNCTION graphql_public.graphql("operationName" text, query text, variables jsonb, extensions jsonb) TO anon;
GRANT ALL ON FUNCTION graphql_public.graphql("operationName" text, query text, variables jsonb, extensions jsonb) TO authenticated;
GRANT ALL ON FUNCTION graphql_public.graphql("operationName" text, query text, variables jsonb, extensions jsonb) TO service_role;


--
-- Name: FUNCTION get_auth(p_usename text); Type: ACL; Schema: pgbouncer; Owner: supabase_admin
--

REVOKE ALL ON FUNCTION pgbouncer.get_auth(p_usename text) FROM PUBLIC;
GRANT ALL ON FUNCTION pgbouncer.get_auth(p_usename text) TO pgbouncer;
GRANT ALL ON FUNCTION pgbouncer.get_auth(p_usename text) TO postgres;


--
-- Name: FUNCTION add_author_daily_quota_for_prompt_usage(user_uuid_param uuid, prompt_id_param uuid, access_password text); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.add_author_daily_quota_for_prompt_usage(user_uuid_param uuid, prompt_id_param uuid, access_password text) TO anon;
GRANT ALL ON FUNCTION public.add_author_daily_quota_for_prompt_usage(user_uuid_param uuid, prompt_id_param uuid, access_password text) TO authenticated;
GRANT ALL ON FUNCTION public.add_author_daily_quota_for_prompt_usage(user_uuid_param uuid, prompt_id_param uuid, access_password text) TO service_role;


--
-- Name: FUNCTION add_reward_quota(user_uuid uuid, reward_amount integer, access_password text); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.add_reward_quota(user_uuid uuid, reward_amount integer, access_password text) TO anon;
GRANT ALL ON FUNCTION public.add_reward_quota(user_uuid uuid, reward_amount integer, access_password text) TO authenticated;
GRANT ALL ON FUNCTION public.add_reward_quota(user_uuid uuid, reward_amount integer, access_password text) TO service_role;


--
-- Name: FUNCTION create_user_membership(); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.create_user_membership() TO anon;
GRANT ALL ON FUNCTION public.create_user_membership() TO authenticated;
GRANT ALL ON FUNCTION public.create_user_membership() TO service_role;


--
-- Name: FUNCTION create_user_prompt(title_param text, description_param text, type_param text, content_param text, user_email_param text, user_display_name_param text, access_password text); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.create_user_prompt(title_param text, description_param text, type_param text, content_param text, user_email_param text, user_display_name_param text, access_password text) TO anon;
GRANT ALL ON FUNCTION public.create_user_prompt(title_param text, description_param text, type_param text, content_param text, user_email_param text, user_display_name_param text, access_password text) TO authenticated;
GRANT ALL ON FUNCTION public.create_user_prompt(title_param text, description_param text, type_param text, content_param text, user_email_param text, user_display_name_param text, access_password text) TO service_role;


--
-- Name: FUNCTION deduct_daily_free_quota(user_uuid uuid, deduct_amount integer, access_password text); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.deduct_daily_free_quota(user_uuid uuid, deduct_amount integer, access_password text) TO anon;
GRANT ALL ON FUNCTION public.deduct_daily_free_quota(user_uuid uuid, deduct_amount integer, access_password text) TO authenticated;
GRANT ALL ON FUNCTION public.deduct_daily_free_quota(user_uuid uuid, deduct_amount integer, access_password text) TO service_role;


--
-- Name: FUNCTION deduct_user_word_count(user_uuid uuid, deduct_amount integer, access_password text); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.deduct_user_word_count(user_uuid uuid, deduct_amount integer, access_password text) TO anon;
GRANT ALL ON FUNCTION public.deduct_user_word_count(user_uuid uuid, deduct_amount integer, access_password text) TO authenticated;
GRANT ALL ON FUNCTION public.deduct_user_word_count(user_uuid uuid, deduct_amount integer, access_password text) TO service_role;


--
-- Name: FUNCTION delete_user_prompt(prompt_id_param uuid, user_email_param text, access_password text); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.delete_user_prompt(prompt_id_param uuid, user_email_param text, access_password text) TO anon;
GRANT ALL ON FUNCTION public.delete_user_prompt(prompt_id_param uuid, user_email_param text, access_password text) TO authenticated;
GRANT ALL ON FUNCTION public.delete_user_prompt(prompt_id_param uuid, user_email_param text, access_password text) TO service_role;


--
-- Name: FUNCTION get_prompt_content(prompt_id_param uuid, access_password text, user_email_param text); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.get_prompt_content(prompt_id_param uuid, access_password text, user_email_param text) TO anon;
GRANT ALL ON FUNCTION public.get_prompt_content(prompt_id_param uuid, access_password text, user_email_param text) TO authenticated;
GRANT ALL ON FUNCTION public.get_prompt_content(prompt_id_param uuid, access_password text, user_email_param text) TO service_role;


--
-- Name: FUNCTION get_user_quota_info(user_uuid uuid, access_password text); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.get_user_quota_info(user_uuid uuid, access_password text) TO anon;
GRANT ALL ON FUNCTION public.get_user_quota_info(user_uuid uuid, access_password text) TO authenticated;
GRANT ALL ON FUNCTION public.get_user_quota_info(user_uuid uuid, access_password text) TO service_role;


--
-- Name: FUNCTION increment_prompt_usage(prompt_id_param uuid, access_password text); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.increment_prompt_usage(prompt_id_param uuid, access_password text) TO anon;
GRANT ALL ON FUNCTION public.increment_prompt_usage(prompt_id_param uuid, access_password text) TO authenticated;
GRANT ALL ON FUNCTION public.increment_prompt_usage(prompt_id_param uuid, access_password text) TO service_role;


--
-- Name: FUNCTION reset_all_user_quotas(); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.reset_all_user_quotas() TO anon;
GRANT ALL ON FUNCTION public.reset_all_user_quotas() TO authenticated;
GRANT ALL ON FUNCTION public.reset_all_user_quotas() TO service_role;


--
-- Name: FUNCTION set_initial_title(); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.set_initial_title() TO anon;
GRANT ALL ON FUNCTION public.set_initial_title() TO authenticated;
GRANT ALL ON FUNCTION public.set_initial_title() TO service_role;


--
-- Name: FUNCTION set_user_verification_status(user_uuid uuid, verified_status boolean, free_quota integer); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.set_user_verification_status(user_uuid uuid, verified_status boolean, free_quota integer) TO anon;
GRANT ALL ON FUNCTION public.set_user_verification_status(user_uuid uuid, verified_status boolean, free_quota integer) TO authenticated;
GRANT ALL ON FUNCTION public.set_user_verification_status(user_uuid uuid, verified_status boolean, free_quota integer) TO service_role;


--
-- Name: FUNCTION sync_membership_to_look(); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.sync_membership_to_look() TO anon;
GRANT ALL ON FUNCTION public.sync_membership_to_look() TO authenticated;
GRANT ALL ON FUNCTION public.sync_membership_to_look() TO service_role;


--
-- Name: FUNCTION sync_prompt_title(); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.sync_prompt_title() TO anon;
GRANT ALL ON FUNCTION public.sync_prompt_title() TO authenticated;
GRANT ALL ON FUNCTION public.sync_prompt_title() TO service_role;


--
-- Name: FUNCTION sync_usage_count_to_zhanshi(); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.sync_usage_count_to_zhanshi() TO anon;
GRANT ALL ON FUNCTION public.sync_usage_count_to_zhanshi() TO authenticated;
GRANT ALL ON FUNCTION public.sync_usage_count_to_zhanshi() TO service_role;


--
-- Name: FUNCTION update_user_prompt(prompt_id_param uuid, title_param text, description_param text, type_param text, content_param text, user_email_param text, access_password text); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.update_user_prompt(prompt_id_param uuid, title_param text, description_param text, type_param text, content_param text, user_email_param text, access_password text) TO anon;
GRANT ALL ON FUNCTION public.update_user_prompt(prompt_id_param uuid, title_param text, description_param text, type_param text, content_param text, user_email_param text, access_password text) TO authenticated;
GRANT ALL ON FUNCTION public.update_user_prompt(prompt_id_param uuid, title_param text, description_param text, type_param text, content_param text, user_email_param text, access_password text) TO service_role;


--
-- Name: FUNCTION apply_rls(wal jsonb, max_record_bytes integer); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) TO postgres;
GRANT ALL ON FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) TO anon;
GRANT ALL ON FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) TO authenticated;
GRANT ALL ON FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) TO service_role;
GRANT ALL ON FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) TO supabase_realtime_admin;


--
-- Name: FUNCTION broadcast_changes(topic_name text, event_name text, operation text, table_name text, table_schema text, new record, old record, level text); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.broadcast_changes(topic_name text, event_name text, operation text, table_name text, table_schema text, new record, old record, level text) TO postgres;
GRANT ALL ON FUNCTION realtime.broadcast_changes(topic_name text, event_name text, operation text, table_name text, table_schema text, new record, old record, level text) TO dashboard_user;


--
-- Name: FUNCTION build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) TO postgres;
GRANT ALL ON FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) TO anon;
GRANT ALL ON FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) TO authenticated;
GRANT ALL ON FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) TO service_role;
GRANT ALL ON FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) TO supabase_realtime_admin;


--
-- Name: FUNCTION "cast"(val text, type_ regtype); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime."cast"(val text, type_ regtype) TO postgres;
GRANT ALL ON FUNCTION realtime."cast"(val text, type_ regtype) TO dashboard_user;
GRANT ALL ON FUNCTION realtime."cast"(val text, type_ regtype) TO anon;
GRANT ALL ON FUNCTION realtime."cast"(val text, type_ regtype) TO authenticated;
GRANT ALL ON FUNCTION realtime."cast"(val text, type_ regtype) TO service_role;
GRANT ALL ON FUNCTION realtime."cast"(val text, type_ regtype) TO supabase_realtime_admin;


--
-- Name: FUNCTION check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) TO postgres;
GRANT ALL ON FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) TO anon;
GRANT ALL ON FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) TO authenticated;
GRANT ALL ON FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) TO service_role;
GRANT ALL ON FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) TO supabase_realtime_admin;


--
-- Name: FUNCTION is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) TO postgres;
GRANT ALL ON FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) TO anon;
GRANT ALL ON FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) TO authenticated;
GRANT ALL ON FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) TO service_role;
GRANT ALL ON FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) TO supabase_realtime_admin;


--
-- Name: FUNCTION list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) TO postgres;
GRANT ALL ON FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) TO anon;
GRANT ALL ON FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) TO authenticated;
GRANT ALL ON FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) TO service_role;
GRANT ALL ON FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) TO supabase_realtime_admin;


--
-- Name: FUNCTION quote_wal2json(entity regclass); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.quote_wal2json(entity regclass) TO postgres;
GRANT ALL ON FUNCTION realtime.quote_wal2json(entity regclass) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.quote_wal2json(entity regclass) TO anon;
GRANT ALL ON FUNCTION realtime.quote_wal2json(entity regclass) TO authenticated;
GRANT ALL ON FUNCTION realtime.quote_wal2json(entity regclass) TO service_role;
GRANT ALL ON FUNCTION realtime.quote_wal2json(entity regclass) TO supabase_realtime_admin;


--
-- Name: FUNCTION send(payload jsonb, event text, topic text, private boolean); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.send(payload jsonb, event text, topic text, private boolean) TO postgres;
GRANT ALL ON FUNCTION realtime.send(payload jsonb, event text, topic text, private boolean) TO dashboard_user;


--
-- Name: FUNCTION subscription_check_filters(); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.subscription_check_filters() TO postgres;
GRANT ALL ON FUNCTION realtime.subscription_check_filters() TO dashboard_user;
GRANT ALL ON FUNCTION realtime.subscription_check_filters() TO anon;
GRANT ALL ON FUNCTION realtime.subscription_check_filters() TO authenticated;
GRANT ALL ON FUNCTION realtime.subscription_check_filters() TO service_role;
GRANT ALL ON FUNCTION realtime.subscription_check_filters() TO supabase_realtime_admin;


--
-- Name: FUNCTION to_regrole(role_name text); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.to_regrole(role_name text) TO postgres;
GRANT ALL ON FUNCTION realtime.to_regrole(role_name text) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.to_regrole(role_name text) TO anon;
GRANT ALL ON FUNCTION realtime.to_regrole(role_name text) TO authenticated;
GRANT ALL ON FUNCTION realtime.to_regrole(role_name text) TO service_role;
GRANT ALL ON FUNCTION realtime.to_regrole(role_name text) TO supabase_realtime_admin;


--
-- Name: FUNCTION topic(); Type: ACL; Schema: realtime; Owner: supabase_realtime_admin
--

GRANT ALL ON FUNCTION realtime.topic() TO postgres;
GRANT ALL ON FUNCTION realtime.topic() TO dashboard_user;


--
-- Name: FUNCTION http_request(); Type: ACL; Schema: supabase_functions; Owner: supabase_functions_admin
--

REVOKE ALL ON FUNCTION supabase_functions.http_request() FROM PUBLIC;
GRANT ALL ON FUNCTION supabase_functions.http_request() TO anon;
GRANT ALL ON FUNCTION supabase_functions.http_request() TO authenticated;
GRANT ALL ON FUNCTION supabase_functions.http_request() TO service_role;
GRANT ALL ON FUNCTION supabase_functions.http_request() TO postgres;


--
-- Name: FUNCTION _crypto_aead_det_decrypt(message bytea, additional bytea, key_id bigint, context bytea, nonce bytea); Type: ACL; Schema: vault; Owner: supabase_admin
--

GRANT ALL ON FUNCTION vault._crypto_aead_det_decrypt(message bytea, additional bytea, key_id bigint, context bytea, nonce bytea) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION create_secret(new_secret text, new_name text, new_description text, new_key_id uuid); Type: ACL; Schema: vault; Owner: supabase_admin
--

GRANT ALL ON FUNCTION vault.create_secret(new_secret text, new_name text, new_description text, new_key_id uuid) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION update_secret(secret_id uuid, new_secret text, new_name text, new_description text, new_key_id uuid); Type: ACL; Schema: vault; Owner: supabase_admin
--

GRANT ALL ON FUNCTION vault.update_secret(secret_id uuid, new_secret text, new_name text, new_description text, new_key_id uuid) TO postgres WITH GRANT OPTION;


--
-- Name: TABLE audit_log_entries; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.audit_log_entries TO dashboard_user;
GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.audit_log_entries TO postgres;
GRANT SELECT ON TABLE auth.audit_log_entries TO postgres WITH GRANT OPTION;


--
-- Name: TABLE flow_state; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.flow_state TO postgres;
GRANT SELECT ON TABLE auth.flow_state TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.flow_state TO dashboard_user;


--
-- Name: TABLE identities; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.identities TO postgres;
GRANT SELECT ON TABLE auth.identities TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.identities TO dashboard_user;


--
-- Name: TABLE instances; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.instances TO dashboard_user;
GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.instances TO postgres;
GRANT SELECT ON TABLE auth.instances TO postgres WITH GRANT OPTION;


--
-- Name: TABLE mfa_amr_claims; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.mfa_amr_claims TO postgres;
GRANT SELECT ON TABLE auth.mfa_amr_claims TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.mfa_amr_claims TO dashboard_user;


--
-- Name: TABLE mfa_challenges; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.mfa_challenges TO postgres;
GRANT SELECT ON TABLE auth.mfa_challenges TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.mfa_challenges TO dashboard_user;


--
-- Name: TABLE mfa_factors; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.mfa_factors TO postgres;
GRANT SELECT ON TABLE auth.mfa_factors TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.mfa_factors TO dashboard_user;


--
-- Name: TABLE one_time_tokens; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.one_time_tokens TO postgres;
GRANT SELECT ON TABLE auth.one_time_tokens TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.one_time_tokens TO dashboard_user;


--
-- Name: TABLE refresh_tokens; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.refresh_tokens TO dashboard_user;
GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.refresh_tokens TO postgres;
GRANT SELECT ON TABLE auth.refresh_tokens TO postgres WITH GRANT OPTION;


--
-- Name: SEQUENCE refresh_tokens_id_seq; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON SEQUENCE auth.refresh_tokens_id_seq TO dashboard_user;
GRANT ALL ON SEQUENCE auth.refresh_tokens_id_seq TO postgres;


--
-- Name: TABLE saml_providers; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.saml_providers TO postgres;
GRANT SELECT ON TABLE auth.saml_providers TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.saml_providers TO dashboard_user;


--
-- Name: TABLE saml_relay_states; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.saml_relay_states TO postgres;
GRANT SELECT ON TABLE auth.saml_relay_states TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.saml_relay_states TO dashboard_user;


--
-- Name: TABLE schema_migrations; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.schema_migrations TO dashboard_user;
GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.schema_migrations TO postgres;
GRANT SELECT ON TABLE auth.schema_migrations TO postgres WITH GRANT OPTION;


--
-- Name: TABLE sessions; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.sessions TO postgres;
GRANT SELECT ON TABLE auth.sessions TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.sessions TO dashboard_user;


--
-- Name: TABLE sso_domains; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.sso_domains TO postgres;
GRANT SELECT ON TABLE auth.sso_domains TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.sso_domains TO dashboard_user;


--
-- Name: TABLE sso_providers; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.sso_providers TO postgres;
GRANT SELECT ON TABLE auth.sso_providers TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.sso_providers TO dashboard_user;


--
-- Name: TABLE users; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.users TO dashboard_user;
GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.users TO postgres;
GRANT SELECT ON TABLE auth.users TO postgres WITH GRANT OPTION;


--
-- Name: TABLE job; Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT SELECT ON TABLE cron.job TO postgres WITH GRANT OPTION;


--
-- Name: TABLE job_run_details; Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT ALL ON TABLE cron.job_run_details TO postgres WITH GRANT OPTION;


--
-- Name: TABLE pg_stat_statements; Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON TABLE extensions.pg_stat_statements TO postgres WITH GRANT OPTION;


--
-- Name: TABLE pg_stat_statements_info; Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON TABLE extensions.pg_stat_statements_info TO postgres WITH GRANT OPTION;


--
-- Name: TABLE "membership-look"; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public."membership-look" TO anon;
GRANT ALL ON TABLE public."membership-look" TO authenticated;
GRANT ALL ON TABLE public."membership-look" TO service_role;


--
-- Name: TABLE "membership-true"; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public."membership-true" TO anon;
GRANT ALL ON TABLE public."membership-true" TO authenticated;
GRANT ALL ON TABLE public."membership-true" TO service_role;


--
-- Name: TABLE novel_files; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.novel_files TO anon;
GRANT ALL ON TABLE public.novel_files TO authenticated;
GRANT ALL ON TABLE public.novel_files TO service_role;


--
-- Name: TABLE "propmt-neirong"; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public."propmt-neirong" TO anon;
GRANT ALL ON TABLE public."propmt-neirong" TO authenticated;
GRANT ALL ON TABLE public."propmt-neirong" TO service_role;


--
-- Name: TABLE "propmt-zhanshi"; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public."propmt-zhanshi" TO anon;
GRANT ALL ON TABLE public."propmt-zhanshi" TO authenticated;
GRANT ALL ON TABLE public."propmt-zhanshi" TO service_role;


--
-- Name: TABLE messages; Type: ACL; Schema: realtime; Owner: supabase_realtime_admin
--

GRANT ALL ON TABLE realtime.messages TO postgres;
GRANT ALL ON TABLE realtime.messages TO dashboard_user;
GRANT SELECT,INSERT,UPDATE ON TABLE realtime.messages TO anon;
GRANT SELECT,INSERT,UPDATE ON TABLE realtime.messages TO authenticated;
GRANT SELECT,INSERT,UPDATE ON TABLE realtime.messages TO service_role;


--
-- Name: TABLE schema_migrations; Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON TABLE realtime.schema_migrations TO postgres;
GRANT ALL ON TABLE realtime.schema_migrations TO dashboard_user;
GRANT SELECT ON TABLE realtime.schema_migrations TO anon;
GRANT SELECT ON TABLE realtime.schema_migrations TO authenticated;
GRANT SELECT ON TABLE realtime.schema_migrations TO service_role;
GRANT ALL ON TABLE realtime.schema_migrations TO supabase_realtime_admin;


--
-- Name: TABLE subscription; Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON TABLE realtime.subscription TO postgres;
GRANT ALL ON TABLE realtime.subscription TO dashboard_user;
GRANT SELECT ON TABLE realtime.subscription TO anon;
GRANT SELECT ON TABLE realtime.subscription TO authenticated;
GRANT SELECT ON TABLE realtime.subscription TO service_role;
GRANT ALL ON TABLE realtime.subscription TO supabase_realtime_admin;


--
-- Name: SEQUENCE subscription_id_seq; Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON SEQUENCE realtime.subscription_id_seq TO postgres;
GRANT ALL ON SEQUENCE realtime.subscription_id_seq TO dashboard_user;
GRANT USAGE ON SEQUENCE realtime.subscription_id_seq TO anon;
GRANT USAGE ON SEQUENCE realtime.subscription_id_seq TO authenticated;
GRANT USAGE ON SEQUENCE realtime.subscription_id_seq TO service_role;
GRANT ALL ON SEQUENCE realtime.subscription_id_seq TO supabase_realtime_admin;


--
-- Name: TABLE buckets; Type: ACL; Schema: storage; Owner: supabase_storage_admin
--

GRANT ALL ON TABLE storage.buckets TO anon;
GRANT ALL ON TABLE storage.buckets TO authenticated;
GRANT ALL ON TABLE storage.buckets TO service_role;
GRANT ALL ON TABLE storage.buckets TO postgres;


--
-- Name: TABLE buckets_analytics; Type: ACL; Schema: storage; Owner: supabase_storage_admin
--

GRANT ALL ON TABLE storage.buckets_analytics TO service_role;
GRANT ALL ON TABLE storage.buckets_analytics TO authenticated;
GRANT ALL ON TABLE storage.buckets_analytics TO anon;


--
-- Name: TABLE iceberg_namespaces; Type: ACL; Schema: storage; Owner: supabase_storage_admin
--

GRANT ALL ON TABLE storage.iceberg_namespaces TO service_role;
GRANT SELECT ON TABLE storage.iceberg_namespaces TO authenticated;
GRANT SELECT ON TABLE storage.iceberg_namespaces TO anon;


--
-- Name: TABLE iceberg_tables; Type: ACL; Schema: storage; Owner: supabase_storage_admin
--

GRANT ALL ON TABLE storage.iceberg_tables TO service_role;
GRANT SELECT ON TABLE storage.iceberg_tables TO authenticated;
GRANT SELECT ON TABLE storage.iceberg_tables TO anon;


--
-- Name: TABLE migrations; Type: ACL; Schema: storage; Owner: supabase_storage_admin
--

GRANT ALL ON TABLE storage.migrations TO anon;
GRANT ALL ON TABLE storage.migrations TO authenticated;
GRANT ALL ON TABLE storage.migrations TO service_role;
GRANT ALL ON TABLE storage.migrations TO postgres;


--
-- Name: TABLE objects; Type: ACL; Schema: storage; Owner: supabase_storage_admin
--

GRANT ALL ON TABLE storage.objects TO anon;
GRANT ALL ON TABLE storage.objects TO authenticated;
GRANT ALL ON TABLE storage.objects TO service_role;
GRANT ALL ON TABLE storage.objects TO postgres;


--
-- Name: TABLE prefixes; Type: ACL; Schema: storage; Owner: supabase_storage_admin
--

GRANT ALL ON TABLE storage.prefixes TO service_role;
GRANT ALL ON TABLE storage.prefixes TO authenticated;
GRANT ALL ON TABLE storage.prefixes TO anon;


--
-- Name: TABLE s3_multipart_uploads; Type: ACL; Schema: storage; Owner: supabase_storage_admin
--

GRANT ALL ON TABLE storage.s3_multipart_uploads TO service_role;
GRANT SELECT ON TABLE storage.s3_multipart_uploads TO authenticated;
GRANT SELECT ON TABLE storage.s3_multipart_uploads TO anon;


--
-- Name: TABLE s3_multipart_uploads_parts; Type: ACL; Schema: storage; Owner: supabase_storage_admin
--

GRANT ALL ON TABLE storage.s3_multipart_uploads_parts TO service_role;
GRANT SELECT ON TABLE storage.s3_multipart_uploads_parts TO authenticated;
GRANT SELECT ON TABLE storage.s3_multipart_uploads_parts TO anon;


--
-- Name: TABLE hooks; Type: ACL; Schema: supabase_functions; Owner: supabase_functions_admin
--

GRANT ALL ON TABLE supabase_functions.hooks TO anon;
GRANT ALL ON TABLE supabase_functions.hooks TO authenticated;
GRANT ALL ON TABLE supabase_functions.hooks TO service_role;


--
-- Name: SEQUENCE hooks_id_seq; Type: ACL; Schema: supabase_functions; Owner: supabase_functions_admin
--

GRANT ALL ON SEQUENCE supabase_functions.hooks_id_seq TO anon;
GRANT ALL ON SEQUENCE supabase_functions.hooks_id_seq TO authenticated;
GRANT ALL ON SEQUENCE supabase_functions.hooks_id_seq TO service_role;


--
-- Name: TABLE migrations; Type: ACL; Schema: supabase_functions; Owner: supabase_functions_admin
--

GRANT ALL ON TABLE supabase_functions.migrations TO anon;
GRANT ALL ON TABLE supabase_functions.migrations TO authenticated;
GRANT ALL ON TABLE supabase_functions.migrations TO service_role;


--
-- Name: TABLE secrets; Type: ACL; Schema: vault; Owner: supabase_admin
--

GRANT SELECT,DELETE ON TABLE vault.secrets TO postgres WITH GRANT OPTION;


--
-- Name: TABLE decrypted_secrets; Type: ACL; Schema: vault; Owner: supabase_admin
--

GRANT SELECT,DELETE ON TABLE vault.decrypted_secrets TO postgres WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: auth; Owner: supabase_auth_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_auth_admin IN SCHEMA auth GRANT ALL ON SEQUENCES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_auth_admin IN SCHEMA auth GRANT ALL ON SEQUENCES  TO dashboard_user;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: auth; Owner: supabase_auth_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_auth_admin IN SCHEMA auth GRANT ALL ON FUNCTIONS  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_auth_admin IN SCHEMA auth GRANT ALL ON FUNCTIONS  TO dashboard_user;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: auth; Owner: supabase_auth_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_auth_admin IN SCHEMA auth GRANT ALL ON TABLES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_auth_admin IN SCHEMA auth GRANT ALL ON TABLES  TO dashboard_user;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: cron; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA cron GRANT ALL ON SEQUENCES  TO postgres WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: cron; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA cron GRANT ALL ON FUNCTIONS  TO postgres WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: cron; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA cron GRANT ALL ON TABLES  TO postgres WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: extensions; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA extensions GRANT ALL ON SEQUENCES  TO postgres WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: extensions; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA extensions GRANT ALL ON FUNCTIONS  TO postgres WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: extensions; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA extensions GRANT ALL ON TABLES  TO postgres WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: graphql; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON SEQUENCES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON SEQUENCES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON SEQUENCES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON SEQUENCES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: graphql; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON FUNCTIONS  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON FUNCTIONS  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON FUNCTIONS  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON FUNCTIONS  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: graphql; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON TABLES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON TABLES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON TABLES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON TABLES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: graphql_public; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON SEQUENCES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON SEQUENCES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON SEQUENCES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON SEQUENCES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: graphql_public; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON FUNCTIONS  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON FUNCTIONS  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON FUNCTIONS  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON FUNCTIONS  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: graphql_public; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON TABLES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON TABLES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON TABLES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON TABLES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON SEQUENCES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON SEQUENCES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON SEQUENCES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON SEQUENCES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: public; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON SEQUENCES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON SEQUENCES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON SEQUENCES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON SEQUENCES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON FUNCTIONS  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON FUNCTIONS  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON FUNCTIONS  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON FUNCTIONS  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: public; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON FUNCTIONS  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON FUNCTIONS  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON FUNCTIONS  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON FUNCTIONS  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON TABLES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON TABLES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON TABLES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON TABLES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: public; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON TABLES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON TABLES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON TABLES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON TABLES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: realtime; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA realtime GRANT ALL ON SEQUENCES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA realtime GRANT ALL ON SEQUENCES  TO dashboard_user;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: realtime; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA realtime GRANT ALL ON FUNCTIONS  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA realtime GRANT ALL ON FUNCTIONS  TO dashboard_user;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: realtime; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA realtime GRANT ALL ON TABLES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA realtime GRANT ALL ON TABLES  TO dashboard_user;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: storage; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON SEQUENCES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON SEQUENCES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON SEQUENCES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON SEQUENCES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: storage; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON FUNCTIONS  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON FUNCTIONS  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON FUNCTIONS  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON FUNCTIONS  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: storage; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON TABLES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON TABLES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON TABLES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON TABLES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: supabase_functions; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA supabase_functions GRANT ALL ON SEQUENCES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA supabase_functions GRANT ALL ON SEQUENCES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA supabase_functions GRANT ALL ON SEQUENCES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA supabase_functions GRANT ALL ON SEQUENCES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: supabase_functions; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA supabase_functions GRANT ALL ON FUNCTIONS  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA supabase_functions GRANT ALL ON FUNCTIONS  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA supabase_functions GRANT ALL ON FUNCTIONS  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA supabase_functions GRANT ALL ON FUNCTIONS  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: supabase_functions; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA supabase_functions GRANT ALL ON TABLES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA supabase_functions GRANT ALL ON TABLES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA supabase_functions GRANT ALL ON TABLES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA supabase_functions GRANT ALL ON TABLES  TO service_role;


--
-- Name: issue_graphql_placeholder; Type: EVENT TRIGGER; Schema: -; Owner: supabase_admin
--

CREATE EVENT TRIGGER issue_graphql_placeholder ON sql_drop
         WHEN TAG IN ('DROP EXTENSION')
   EXECUTE FUNCTION extensions.set_graphql_placeholder();


ALTER EVENT TRIGGER issue_graphql_placeholder OWNER TO supabase_admin;

--
-- Name: issue_pg_cron_access; Type: EVENT TRIGGER; Schema: -; Owner: supabase_admin
--

CREATE EVENT TRIGGER issue_pg_cron_access ON ddl_command_end
         WHEN TAG IN ('CREATE EXTENSION')
   EXECUTE FUNCTION extensions.grant_pg_cron_access();


ALTER EVENT TRIGGER issue_pg_cron_access OWNER TO supabase_admin;

--
-- Name: issue_pg_graphql_access; Type: EVENT TRIGGER; Schema: -; Owner: supabase_admin
--

CREATE EVENT TRIGGER issue_pg_graphql_access ON ddl_command_end
         WHEN TAG IN ('CREATE FUNCTION')
   EXECUTE FUNCTION extensions.grant_pg_graphql_access();


ALTER EVENT TRIGGER issue_pg_graphql_access OWNER TO supabase_admin;

--
-- Name: issue_pg_net_access; Type: EVENT TRIGGER; Schema: -; Owner: postgres
--

CREATE EVENT TRIGGER issue_pg_net_access ON ddl_command_end
         WHEN TAG IN ('CREATE EXTENSION')
   EXECUTE FUNCTION extensions.grant_pg_net_access();


ALTER EVENT TRIGGER issue_pg_net_access OWNER TO postgres;

--
-- Name: pgrst_ddl_watch; Type: EVENT TRIGGER; Schema: -; Owner: supabase_admin
--

CREATE EVENT TRIGGER pgrst_ddl_watch ON ddl_command_end
   EXECUTE FUNCTION extensions.pgrst_ddl_watch();


ALTER EVENT TRIGGER pgrst_ddl_watch OWNER TO supabase_admin;

--
-- Name: pgrst_drop_watch; Type: EVENT TRIGGER; Schema: -; Owner: supabase_admin
--

CREATE EVENT TRIGGER pgrst_drop_watch ON sql_drop
   EXECUTE FUNCTION extensions.pgrst_drop_watch();


ALTER EVENT TRIGGER pgrst_drop_watch OWNER TO supabase_admin;

--
-- PostgreSQL database dump complete
--

